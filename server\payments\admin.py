from django.contrib import admin
from .models import Payment, PaymentWebhook


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'order', 'amount', 'currency', 'payment_method', 'status', 'created_at')
    list_filter = ('status', 'payment_method', 'currency', 'created_at')
    search_fields = ('user__email', 'order__order_number', 'paystack_reference')
    readonly_fields = ('id', 'created_at', 'updated_at', 'processed_at')
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('Payment Information', {
            'fields': ('id', 'user', 'order', 'amount', 'currency', 'payment_method', 'status')
        }),
        ('Paystack Details', {
            'fields': ('paystack_reference', 'paystack_access_code', 'paystack_response'),
            'classes': ('collapse',)
        }),
        ('Financial Details', {
            'fields': ('transaction_fee', 'net_amount')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'processed_at'),
            'classes': ('collapse',)
        }),
        ('Additional', {
            'fields': ('failure_reason', 'notes'),
            'classes': ('collapse',)
        }),
    )


@admin.register(PaymentWebhook)
class PaymentWebhookAdmin(admin.ModelAdmin):
    list_display = ('event_type', 'event_id', 'reference', 'processed', 'created_at')
    list_filter = ('event_type', 'processed', 'created_at')
    search_fields = ('event_id', 'reference')
    readonly_fields = ('created_at', 'processed_at')
    date_hierarchy = 'created_at'
