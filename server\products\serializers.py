from rest_framework import serializers
from .models import Category, Product, ProductRating


class CategorySerializer(serializers.ModelSerializer):
    """Serializer for product categories"""
    class Meta:
        model = Category
        fields = '__all__'
        read_only_fields = ('slug', 'created_at', 'updated_at')


class ProductSerializer(serializers.ModelSerializer):
    """Serializer for products"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    average_rating = serializers.ReadOnlyField()
    rating_count = serializers.ReadOnlyField()
    is_in_stock = serializers.ReadOnlyField()
    discount_percentage = serializers.ReadOnlyField()
    
    class Meta:
        model = Product
        fields = '__all__'
        read_only_fields = ('slug', 'created_at', 'updated_at', 'average_rating', 
                           'rating_count', 'is_in_stock', 'discount_percentage')


class ProductRatingSerializer(serializers.ModelSerializer):
    """Serializer for product ratings"""
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    
    class Meta:
        model = ProductRating
        fields = '__all__'
        read_only_fields = ('user', 'created_at', 'updated_at')


class ProductListSerializer(serializers.ModelSerializer):
    """Simplified serializer for product lists"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    average_rating = serializers.ReadOnlyField()
    rating_count = serializers.ReadOnlyField()
    
    class Meta:
        model = Product
        fields = ('id', 'name', 'slug', 'short_description', 'category_name', 
                 'price', 'compare_price', 'image', 'is_featured', 'average_rating', 
                 'rating_count', 'is_in_stock', 'discount_percentage')
