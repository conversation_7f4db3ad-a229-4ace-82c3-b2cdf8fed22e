# 🧪 Navigation Testing Guide

## 🚨 Current Issue
The navigation links (Home, Products, Cart) are not working properly. When clicked, they don't switch between sections.

## 🔧 How to Test Navigation

### Step 1: Start the Development Server
```bash
# Option 1: Use the development server script
python start_dev_server.py

# Option 2: Manual start
cd client
python -m http.server 3000
```

### Step 2: Open the Main Application
Open your browser to: `http://localhost:3000`

### Step 3: Test Basic Navigation
1. **Open Browser Console** (Press F12)
2. **Click on navigation links**: Home, Products, Cart
3. **Check console output** for navigation debug messages

### Step 4: Test Navigation Manually
In the browser console, run these commands:

```javascript
// Test if navigation functions exist
console.log(typeof showSection);
console.log(typeof setupNavigation);

// Test navigation manually
showSection('products');
showSection('home');
showSection('cart');

// Debug navigation state
debugNavigation();

// Test navigation automatically
testNavigation();
```

### Step 5: Test with Simple Navigation Page
Open the test page: `http://localhost:3000/test_navigation.html`

This page has a simplified navigation system to verify basic functionality.

## 🔍 Expected Behavior

### When Navigation Works:
1. **Console logs** should show navigation events
2. **Page content** should change when clicking nav links
3. **Active nav link** should be highlighted
4. **URL hash** should update (optional)

### Debug Console Output:
```
Setting up navigation...
Found nav links: 4
Setting up link 0: #home
Setting up link 1: #products
Setting up link 2: #cart
Navigation clicked: #products
Navigating to products
Showing section: products
```

## 🐛 Troubleshooting

### If Navigation Still Doesn't Work:

1. **Check Console Errors:**
   - Look for JavaScript errors in browser console
   - Check if all scripts are loading properly

2. **Verify DOM Elements:**
   ```javascript
   // Check if elements exist
   console.log(document.querySelectorAll('.nav-link'));
   console.log(document.querySelector('.hero'));
   console.log(document.querySelector('main'));
   ```

3. **Test Manual Section Switching:**
   ```javascript
   // Hide all sections manually
   document.querySelector('.hero').style.display = 'none';
   document.querySelectorAll('.featured-section').forEach(s => s.style.display = 'none');
   
   // Show products section manually
   showSection('products');
   ```

4. **Check Event Listeners:**
   ```javascript
   // Check if event listeners are attached
   const links = document.querySelectorAll('.nav-link');
   links.forEach((link, i) => {
       console.log(`Link ${i}:`, link.onclick, link.getAttribute('href'));
   });
   ```

## 🔧 Quick Fixes to Try

### Fix 1: Force Navigation Setup
```javascript
// Run this in browser console
setupNavigation();
addFallbackNavigation();
```

### Fix 2: Manual Navigation
```javascript
// Add manual click handlers
document.querySelector('a[href="#home"]').onclick = () => showSection('home');
document.querySelector('a[href="#products"]').onclick = () => showSection('products');
document.querySelector('a[href="#cart"]').onclick = () => showSection('cart');
```

### Fix 3: Check CSS Display
```javascript
// Check if sections are hidden by CSS
const hero = document.querySelector('.hero');
console.log('Hero display:', getComputedStyle(hero).display);
```

## 📋 Test Checklist

- [ ] Browser console shows navigation setup messages
- [ ] Clicking nav links shows console output
- [ ] Page content changes when clicking nav links
- [ ] Active nav link is highlighted
- [ ] No JavaScript errors in console
- [ ] Test navigation page works properly
- [ ] Manual navigation commands work in console

## 🎯 Success Criteria

Navigation is working when:
1. ✅ Console shows "Navigation clicked: #products" when clicking Products
2. ✅ Page content switches from home to products view
3. ✅ Products section shows with search filters
4. ✅ Cart section shows (with login prompt if not logged in)
5. ✅ Home section shows hero and featured products

## 📞 Next Steps

If navigation still doesn't work after these tests:
1. Check the browser console for specific error messages
2. Try the test navigation page to isolate the issue
3. Run the manual debug commands to identify the problem
4. Report the specific console output for further debugging
