# Generated by Django 5.2.4 on 2025-07-16 07:20

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        ('products', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='userbehavior',
            name='product',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='products.product'),
        ),
        migrations.AddField(
            model_name='userbehavior',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='behaviors', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='userbehavior',
            index=models.Index(fields=['user', 'action'], name='accounts_us_user_id_4cd7e7_idx'),
        ),
        migrations.AddIndex(
            model_name='userbehavior',
            index=models.Index(fields=['product', 'action'], name='accounts_us_product_e3841d_idx'),
        ),
        migrations.AddIndex(
            model_name='userbehavior',
            index=models.Index(fields=['timestamp'], name='accounts_us_timesta_73f935_idx'),
        ),
    ]
