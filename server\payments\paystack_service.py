"""
Paystack payment service for handling payment transactions
"""
import requests
import json
import hashlib
import hmac
from django.conf import settings
from django.utils import timezone
from .models import Payment, PaymentWebhook


class PaystackService:
    """Service class for Paystack payment integration"""
    
    def __init__(self):
        self.secret_key = getattr(settings, 'PAYSTACK_SECRET_KEY', '')
        self.public_key = getattr(settings, 'PAYSTACK_PUBLIC_KEY', '')
        self.base_url = 'https://api.paystack.co'
        
    def get_headers(self):
        """Get headers for Paystack API requests"""
        return {
            'Authorization': f'Bearer {self.secret_key}',
            'Content-Type': 'application/json',
        }
    
    def initialize_payment(self, email, amount, reference, callback_url=None):
        """Initialize a payment transaction"""
        url = f'{self.base_url}/transaction/initialize'
        
        data = {
            'email': email,
            'amount': int(amount * 100),  # Convert to kobo
            'reference': reference,
            'currency': 'NGN',
        }
        
        if callback_url:
            data['callback_url'] = callback_url
        
        try:
            response = requests.post(url, headers=self.get_headers(), json=data)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {'status': False, 'message': str(e)}
    
    def verify_payment(self, reference):
        """Verify a payment transaction"""
        url = f'{self.base_url}/transaction/verify/{reference}'
        
        try:
            response = requests.get(url, headers=self.get_headers())
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {'status': False, 'message': str(e)}
    
    def list_transactions(self, page=1, per_page=50):
        """List transactions"""
        url = f'{self.base_url}/transaction'
        params = {'page': page, 'perPage': per_page}
        
        try:
            response = requests.get(url, headers=self.get_headers(), params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {'status': False, 'message': str(e)}
    
    def verify_webhook_signature(self, payload, signature):
        """Verify webhook signature from Paystack"""
        if not self.secret_key:
            return False
        
        computed_signature = hmac.new(
            self.secret_key.encode('utf-8'),
            payload,
            hashlib.sha512
        ).hexdigest()
        
        return hmac.compare_digest(computed_signature, signature)
    
    def process_webhook(self, payload, signature):
        """Process webhook from Paystack"""
        if not self.verify_webhook_signature(payload, signature):
            return {'status': False, 'message': 'Invalid signature'}
        
        try:
            data = json.loads(payload.decode('utf-8'))
            event_type = data.get('event')
            event_data = data.get('data', {})
            
            # Store webhook event
            webhook = PaymentWebhook.objects.create(
                event_type=event_type,
                event_id=data.get('id', ''),
                reference=event_data.get('reference', ''),
                data=data
            )
            
            # Process the event
            if event_type == 'charge.success':
                self._handle_successful_payment(event_data)
            elif event_type == 'charge.failed':
                self._handle_failed_payment(event_data)
            
            webhook.processed = True
            webhook.processed_at = timezone.now()
            webhook.save()
            
            return {'status': True, 'message': 'Webhook processed successfully'}
            
        except Exception as e:
            return {'status': False, 'message': str(e)}
    
    def _handle_successful_payment(self, data):
        """Handle successful payment webhook"""
        reference = data.get('reference')
        if not reference:
            return
        
        try:
            payment = Payment.objects.get(paystack_reference=reference)
            payment.status = 'success'
            payment.paystack_response = data
            payment.processed_at = timezone.now()
            
            # Update transaction fee if available
            if 'fees' in data:
                payment.transaction_fee = data['fees'] / 100  # Convert from kobo
                payment.net_amount = payment.amount - payment.transaction_fee
            
            payment.save()
            
            # Update order status
            if payment.order:
                payment.order.payment_status = 'paid'
                payment.order.status = 'confirmed'
                payment.order.save()
                
        except Payment.DoesNotExist:
            pass  # Payment not found, might be a test transaction
    
    def _handle_failed_payment(self, data):
        """Handle failed payment webhook"""
        reference = data.get('reference')
        if not reference:
            return
        
        try:
            payment = Payment.objects.get(paystack_reference=reference)
            payment.status = 'failed'
            payment.paystack_response = data
            payment.failure_reason = data.get('gateway_response', 'Payment failed')
            payment.processed_at = timezone.now()
            payment.save()
            
            # Update order status
            if payment.order:
                payment.order.payment_status = 'failed'
                payment.order.save()
                
        except Payment.DoesNotExist:
            pass  # Payment not found


# Global instance
paystack_service = PaystackService()
