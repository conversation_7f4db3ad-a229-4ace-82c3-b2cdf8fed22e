from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from products.serializers import ProductListSerializer
from .models import Recommendation
from .serializers import RecommendationSerializer
from .engine import recommendation_engine


class UserRecommendationsView(generics.ListAPIView):
    """Get recommendations for authenticated user"""
    serializer_class = RecommendationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        user = self.request.user
        rec_type = self.request.query_params.get('type', 'hybrid')
        
        # Update recommendations if needed
        recommendation_engine.update_user_recommendations(user)
        
        return Recommendation.objects.filter(
            user=user,
            is_active=True,
            recommendation_type=rec_type
        ).order_by('-score')[:10]


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def trending_products(request):
    """Get trending products"""
    products = recommendation_engine.trending_products(limit=10)
    serializer = ProductListSerializer(products, many=True)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def personalized_recommendations(request):
    """Get personalized recommendations for user"""
    user = request.user
    rec_type = request.GET.get('type', 'hybrid')
    limit = int(request.GET.get('limit', 10))
    
    products = recommendation_engine.get_user_recommendations(
        user, rec_type, limit
    )
    
    # Convert to products if they're recommendation objects
    if products and hasattr(products[0], 'product'):
        products = [rec.product for rec in products]
    
    serializer = ProductListSerializer(products, many=True)
    return Response({
        'type': rec_type,
        'count': len(products),
        'products': serializer.data
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def update_recommendations(request):
    """Manually update recommendations for user"""
    user = request.user
    count = recommendation_engine.update_user_recommendations(user)
    
    return Response({
        'message': 'Recommendations updated successfully',
        'count': count
    }, status=status.HTTP_200_OK)
