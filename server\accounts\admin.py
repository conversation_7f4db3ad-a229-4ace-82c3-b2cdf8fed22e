from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User, UserProfile, UserBehavior


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    list_display = ('email', 'username', 'first_name', 'last_name', 'is_verified', 'is_staff', 'date_joined')
    list_filter = ('is_staff', 'is_superuser', 'is_active', 'is_verified', 'date_joined')
    search_fields = ('email', 'username', 'first_name', 'last_name')
    ordering = ('-date_joined',)
    
    fieldsets = BaseUserAdmin.fieldsets + (
        ('Additional Info', {'fields': ('phone_number', 'date_of_birth', 'is_verified')}),
    )


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'gender', 'city', 'country', 'created_at')
    list_filter = ('gender', 'country', 'created_at')
    search_fields = ('user__email', 'user__username', 'city', 'country')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(UserBehavior)
class UserBehaviorAdmin(admin.ModelAdmin):
    list_display = ('user', 'action', 'product', 'timestamp')
    list_filter = ('action', 'timestamp')
    search_fields = ('user__email', 'product__name', 'search_query')
    readonly_fields = ('timestamp',)
    date_hierarchy = 'timestamp'
