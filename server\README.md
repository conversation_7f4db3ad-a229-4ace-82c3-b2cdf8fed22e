# E-commerce Backend Server

This is the Django REST Framework backend for the AI-powered e-commerce recommendation system.

## Quick Setup (Minimal Dependencies)

### Option 1: Automatic Setup (Recommended)

**For Windows:**
```bash
# Double-click setup.bat or run in command prompt:
setup.bat
```

**For Mac/Linux:**
```bash
python setup.py
```

### Option 2: Manual Setup

1. **Install Django only:**
   ```bash
   pip install Django
   ```

2. **Run migrations:**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

3. **Create superuser (optional):**
   ```bash
   python manage.py createsuperuser
   ```

4. **Run server:**
   ```bash
   python manage.py runserver
   ```

## Features Working with Minimal Setup

✅ **User Authentication** - Registration, login, logout with JWT
✅ **Product Management** - CRUD operations for products and categories  
✅ **Basic Recommendations** - Simple recommendation engine without ML libraries
✅ **Shopping Cart** - Add/remove items (basic structure)
✅ **Admin Panel** - Django admin interface
✅ **SQLite Database** - No PostgreSQL setup required

## API Endpoints

### Authentication
- `POST /api/auth/register/` - User registration
- `POST /api/auth/login/` - User login  
- `POST /api/auth/logout/` - User logout
- `GET /api/auth/profile/` - Get user profile

### Products
- `GET /api/products/` - List all products
- `GET /api/products/featured/` - Featured products
- `GET /api/products/categories/` - Product categories
- `GET /api/products/<slug>/` - Product details

### Recommendations
- `GET /api/recommendations/trending/` - Trending products
- `GET /api/recommendations/personalized/` - Personalized recommendations (requires auth)

## Testing the API

1. **Start the server:**
   ```bash
   python manage.py runserver
   ```

2. **Test endpoints:**
   - Products: http://127.0.0.1:8000/api/products/
   - Admin: http://127.0.0.1:8000/admin/
   - API Root: http://127.0.0.1:8000/api/

## Adding Sample Data

Run the seed script to add sample products:
```bash
python manage.py shell < seed_data.py
```

## Troubleshooting

### Package Installation Issues
If you get errors installing packages, try:
1. Use only Django: `pip install Django`
2. Install packages one by one: `python install_packages.py`
3. Use the minimal setup which only requires Django

### Database Issues
- The project uses SQLite by default (no setup required)
- Database file: `db.sqlite3`
- To reset: delete `db.sqlite3` and run migrations again

### Import Errors
If you get import errors for missing packages:
1. Check which packages are actually needed
2. Install only the essential ones
3. Comment out unused imports in settings.py

## Next Steps

Once the basic setup works:
1. Install additional packages as needed
2. Switch to PostgreSQL for production
3. Add ML libraries for advanced recommendations
4. Implement payment integration

## Production Deployment

For production:
1. Set `DEBUG=False` in settings
2. Use PostgreSQL database
3. Install all packages from requirements.txt
4. Set up proper environment variables
5. Use gunicorn for serving

## Support

If you encounter issues:
1. Check the error messages carefully
2. Try the minimal setup first
3. Install packages one by one
4. Use SQLite instead of PostgreSQL initially
