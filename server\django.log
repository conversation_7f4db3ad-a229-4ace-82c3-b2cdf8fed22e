Watching for file changes with StatReloader
"GET /api/products/ HTTP/1.1" 200 500
- Broken pipe from ('127.0.0.1', 12913)
"GET /api/products/categories/ HTTP/1.1" 200 522
- Broken pipe from ('127.0.0.1', 12937)
"GET /api/recommendations/trending/ HTTP/1.1" 200 406
"GET /api/products/ HTTP/1.1" 200 500
"GET /api/products/categories/ HTTP/1.1" 200 522
"GET /api/products/featured/ HTTP/1.1" 200 432
"GET /api/products/ HTTP/1.1" 200 500
"GET /api/products/ HTTP/1.1" 200 500
- Broken pipe from ('127.0.0.1', 13507)
"GET /api/products/ HTTP/1.1" 200 2494
"GET /api/recommendations/trending/ HTTP/1.1" 200 2037
"GET /api/products/ HTTP/1.1" 200 2494
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\payments\models.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\payments\urls.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/products/ HTTP/1.1" 200 2494
"GET /api/products/featured/ HTTP/1.1" 200 1181
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4170
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
"GET /static/admin/css/base.css HTTP/1.1" 200 22120
"GET /static/admin/js/theme.js HTTP/1.1" 200 1653
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
"GET /static/admin/css/login.css HTTP/1.1" 200 951
"GET /static/admin/css/responsive.css HTTP/1.1" 200 16565
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 3169
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Not Found: /
"GET / HTTP/1.1" 404 3118
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 3169
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/products/ HTTP/1.1" 200 2494
Method Not Allowed (OPTIONS): /api/auth/register/
"OPTIONS /api/auth/register/ HTTP/1.1" 405 0
Method Not Allowed (OPTIONS): /api/auth/register/
"OPTIONS /api/auth/register/ HTTP/1.1" 405 0
Method Not Allowed (OPTIONS): /api/auth/login/
"OPTIONS /api/auth/login/ HTTP/1.1" 405 0
Method Not Allowed (OPTIONS): /api/auth/register/
"OPTIONS /api/auth/register/ HTTP/1.1" 405 0
Method Not Allowed (OPTIONS): /api/auth/register/
"OPTIONS /api/auth/register/ HTTP/1.1" 405 0
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\ecommerce_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"OPTIONS /api/auth/register/ HTTP/1.1" 200 0
"POST /api/auth/register/ HTTP/1.1" 201 169
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"GET /api/products/ HTTP/1.1" 200 2494
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"OPTIONS /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 200 0
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"OPTIONS /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 200 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"OPTIONS /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 200 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"GET /api/orders/cart/ HTTP/1.1" 302 0
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
Not Found: /accounts/login/
"GET /api/orders/cart/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"OPTIONS /api/orders/cart/add/ HTTP/1.1" 200 0
"POST /api/orders/cart/add/ HTTP/1.1" 302 0
"OPTIONS /accounts/login/?next=/api/orders/cart/add/ HTTP/1.1" 200 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/add/ HTTP/1.1" 404 3208
"POST /api/orders/cart/add/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/add/ HTTP/1.1" 404 3208
"GET /api/orders/cart/ HTTP/1.1" 302 0
"GET /api/orders/cart/ HTTP/1.1" 302 0
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
"POST /api/auth/logout/ HTTP/1.1" 200 32
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
Bad Request: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 400 38
Bad Request: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 400 38
"POST /api/auth/login/ HTTP/1.1" 200 157
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\ecommerce_backend\settings.py changed, reloading.
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\ecommerce_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\ecommerce_backend\settings.py changed, reloading.
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\ecommerce_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 157
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"GET /api/products/ HTTP/1.1" 200 2494
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"POST /api/orders/cart/add/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/add/ HTTP/1.1" 404 3208
"POST /api/auth/login/ HTTP/1.1" 200 157
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"GET /api/products/ HTTP/1.1" 200 2494
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"POST /api/orders/cart/add/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/add/ HTTP/1.1" 404 3208
"POST /api/orders/cart/add/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/add/ HTTP/1.1" 404 3208
"GET /api/orders/cart/ HTTP/1.1" 302 0
"GET /api/orders/cart/ HTTP/1.1" 302 0
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 157
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"GET /api/products/ HTTP/1.1" 200 2494
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\ecommerce_backend\settings.py changed, reloading.
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\ecommerce_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\ecommerce_backend\settings.py changed, reloading.
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\ecommerce_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 157
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"GET /api/products/ HTTP/1.1" 200 2494
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"POST /api/orders/cart/add/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/add/ HTTP/1.1" 404 3208
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 157
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"GET /api/products/ HTTP/1.1" 200 2494
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"POST /api/orders/cart/add/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/add/ HTTP/1.1" 404 3208
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 157
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"GET /api/products/ HTTP/1.1" 200 2494
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"OPTIONS /api/orders/cart/add/ HTTP/1.1" 200 0
"POST /api/orders/cart/add/ HTTP/1.1" 302 0
"OPTIONS /accounts/login/?next=/api/orders/cart/add/ HTTP/1.1" 200 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/add/ HTTP/1.1" 404 3208
"POST /api/orders/cart/add/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/add/ HTTP/1.1" 404 3208
"POST /api/auth/login/ HTTP/1.1" 200 157
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"GET /api/products/ HTTP/1.1" 200 2494
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"POST /api/orders/cart/add/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/add/ HTTP/1.1" 404 3208
"POST /api/orders/cart/add/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/add/ HTTP/1.1" 404 3208
"POST /api/orders/cart/add/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/add/ HTTP/1.1" 404 3208
"POST /api/orders/cart/add/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/add/ HTTP/1.1" 404 3208
"POST /api/orders/cart/add/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/add/ HTTP/1.1" 404 3208
"POST /api/orders/cart/add/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/add/ HTTP/1.1" 404 3208
"POST /api/auth/login/ HTTP/1.1" 200 157
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"GET /api/products/ HTTP/1.1" 200 2494
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
"POST /api/orders/cart/add/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/add/ HTTP/1.1" 404 3208
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 157
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"GET /api/products/ HTTP/1.1" 200 2494
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
"POST /api/auth/behavior/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/auth/behavior/ HTTP/1.1" 404 3206
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\ecommerce_backend\settings.py changed, reloading.
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\ecommerce_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\urls.py changed, reloading.
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\urls.py changed, reloading.
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\views.py changed, reloading.
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\views.py changed, reloading.
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\views.py changed, reloading.
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\views.py changed, reloading.
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\views.py changed, reloading.
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\views.py changed, reloading.
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\views.py changed, reloading.
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\views.py changed, reloading.
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/auth/csrf/ HTTP/1.1" 200 81
"POST /api/auth/login/ HTTP/1.1" 200 157
"POST /api/auth/login/ HTTP/1.1" 200 157
"GET /api/products/ HTTP/1.1" 200 2494
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 157
"POST /api/auth/login/ HTTP/1.1" 200 157
"POST /api/auth/login/ HTTP/1.1" 200 157
"POST /api/auth/login/ HTTP/1.1" 200 157
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"POST /api/auth/login/ HTTP/1.1" 200 157
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /api/orders/cart/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
"POST /api/auth/login/ HTTP/1.1" 200 157
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"GET /api/orders/cart/ HTTP/1.1" 302 0
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
Not Found: /accounts/login/
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"POST /api/auth/login/ HTTP/1.1" 200 157
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"POST /api/auth/login/ HTTP/1.1" 200 157
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"POST /api/auth/login/ HTTP/1.1" 200 157
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"GET /api/auth/csrf/ HTTP/1.1" 200 81
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\recommendations\views_simple.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\recommendations\views_simple.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\recommendations\views_simple.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/auth/csrf/ HTTP/1.1" 200 81
"POST /api/auth/login/ HTTP/1.1" 200 157
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
"GET /api/products/ HTTP/1.1" 200 2494
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/orders/cart/add/
"POST /api/orders/cart/add/ HTTP/1.1" 401 36
Unauthorized: /api/orders/cart/add/
"POST /api/orders/cart/add/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
Unauthorized: /api/auth/behavior/
Unauthorized: /api/auth/behavior/
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
"POST /api/auth/behavior/ HTTP/1.1" 401 36
"POST /api/auth/behavior/ HTTP/1.1" 401 36
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
Unauthorized: /api/auth/behavior/
Unauthorized: /api/auth/behavior/
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
"POST /api/auth/behavior/ HTTP/1.1" 401 36
"POST /api/auth/behavior/ HTTP/1.1" 401 36
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/orders/cart/add/
"POST /api/orders/cart/add/ HTTP/1.1" 401 36
"OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
"POST /api/auth/logout/ HTTP/1.1" 200 32
"OPTIONS /api/auth/register/ HTTP/1.1" 200 0
"POST /api/auth/register/ HTTP/1.1" 201 167
"GET /api/recommendations/personalized/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/recommendations/personalized/ HTTP/1.1" 404 3221
"GET /api/orders/cart/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/api/orders/cart/ HTTP/1.1" 404 3204
Unauthorized: /api/orders/cart/add/
"POST /api/orders/cart/add/ HTTP/1.1" 401 36
Unauthorized: /api/orders/cart/add/
"POST /api/orders/cart/add/ HTTP/1.1" 401 36
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\recommendations\views_simple.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/auth/csrf/ HTTP/1.1" 200 81
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 157
Unauthorized: /api/recommendations/personalized/
"GET /api/recommendations/personalized/ HTTP/1.1" 401 36
Unauthorized: /api/orders/cart/
"GET /api/orders/cart/ HTTP/1.1" 401 36
"GET /api/products/ HTTP/1.1" 200 2494
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
Unauthorized: /api/auth/behavior/
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
"OPTIONS /api/orders/cart/add/ HTTP/1.1" 200 0
Unauthorized: /api/orders/cart/add/
"POST /api/orders/cart/add/ HTTP/1.1" 401 36
Unauthorized: /api/orders/cart/add/
"POST /api/orders/cart/add/ HTTP/1.1" 401 36
Unauthorized: /api/orders/cart/add/
"POST /api/orders/cart/add/ HTTP/1.1" 401 36
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/auth/csrf/ HTTP/1.1" 200 81
"POST /api/auth/login/ HTTP/1.1" 200 238
Unauthorized: /api/recommendations/personalized/
"GET /api/recommendations/personalized/ HTTP/1.1" 401 36
Unauthorized: /api/orders/cart/
"GET /api/orders/cart/ HTTP/1.1" 401 36
"GET /api/products/ HTTP/1.1" 200 2494
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/orders/cart/add/
"POST /api/orders/cart/add/ HTTP/1.1" 401 36
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\ecommerce_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/auth/csrf/ HTTP/1.1" 200 81
"POST /api/auth/login/ HTTP/1.1" 200 238
Unauthorized: /api/recommendations/personalized/
"GET /api/recommendations/personalized/ HTTP/1.1" 401 36
Unauthorized: /api/orders/cart/
"GET /api/orders/cart/ HTTP/1.1" 401 36
"GET /api/products/ HTTP/1.1" 200 2494
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
Unauthorized: /api/auth/behavior/
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
"POST /api/auth/behavior/ HTTP/1.1" 401 36
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/orders/cart/add/
"POST /api/orders/cart/add/ HTTP/1.1" 401 36
Unauthorized: /api/orders/cart/add/
"POST /api/orders/cart/add/ HTTP/1.1" 401 36
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\ecommerce_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\ecommerce_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/auth/csrf/ HTTP/1.1" 200 81
"GET /api/auth/csrf/ HTTP/1.1" 200 81
"POST /api/auth/login/ HTTP/1.1" 200 238
Unauthorized: /api/recommendations/personalized/
"GET /api/recommendations/personalized/ HTTP/1.1" 401 36
Unauthorized: /api/orders/cart/
"GET /api/orders/cart/ HTTP/1.1" 401 36
"GET /api/products/ HTTP/1.1" 200 2494
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/auth/behavior/
"POST /api/auth/behavior/ HTTP/1.1" 401 36
Unauthorized: /api/orders/cart/add/
"POST /api/orders/cart/add/ HTTP/1.1" 401 36
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/auth/csrf/ HTTP/1.1" 200 81
"POST /api/auth/login/ HTTP/1.1" 200 291
Unauthorized: /api/recommendations/personalized/
"GET /api/recommendations/personalized/ HTTP/1.1" 401 36
Unauthorized: /api/orders/cart/
"GET /api/orders/cart/ HTTP/1.1" 401 36
"GET /api/products/ HTTP/1.1" 200 2494
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/orders/cart/add/ HTTP/1.1" 200 0
"OPTIONS /api/orders/cart/add/ HTTP/1.1" 200 0
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\accounts\views_simple.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\ecommerce_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/auth/csrf/ HTTP/1.1" 200 81
"POST /api/auth/login/ HTTP/1.1" 200 291
Unauthorized: /api/recommendations/personalized/
"GET /api/recommendations/personalized/ HTTP/1.1" 401 36
Unauthorized: /api/orders/cart/
"GET /api/orders/cart/ HTTP/1.1" 401 36
"GET /api/products/ HTTP/1.1" 200 2494
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"OPTIONS /api/auth/behavior/ HTTP/1.1" 200 0
"POST /api/auth/behavior/ HTTP/1.1" 200 53
"POST /api/auth/behavior/ HTTP/1.1" 200 53
"POST /api/auth/behavior/ HTTP/1.1" 200 53
"POST /api/auth/behavior/ HTTP/1.1" 200 53
"POST /api/auth/behavior/ HTTP/1.1" 200 53
"POST /api/auth/behavior/ HTTP/1.1" 200 53
"OPTIONS /api/orders/cart/add/ HTTP/1.1" 200 0
Unauthorized: /api/orders/cart/add/
"POST /api/orders/cart/add/ HTTP/1.1" 401 36
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\recommendations\views_simple.py changed, reloading.
Watching for file changes with StatReloader
Not Found: /
"GET / HTTP/1.1" 404 3118
"GET /api/auth/csrf/ HTTP/1.1" 200 81
"POST /api/auth/login/ HTTP/1.1" 200 291
"OPTIONS /api/recommendations/personalized/ HTTP/1.1" 200 0
Internal Server Error: /api/recommendations/personalized/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Project 911\Ecommerce\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\Project 911\Ecommerce\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\recommendations\views_simple.py", line 30, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\recommendations\views_simple.py", line 86, in personalized_recommendations
    ).exclude(
      ~~~~~~~^
        behaviors__user=user,
        ^^^^^^^^^^^^^^^^^^^^^
        behaviors__action='purchase'
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ).order_by('-created_at')[:limit]
    ^
  File "C:\Users\<USER>\Desktop\Project 911\Ecommerce\venv\Lib\site-packages\django\db\models\query.py", line 1501, in exclude
    return self._filter_or_exclude(True, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Project 911\Ecommerce\venv\Lib\site-packages\django\db\models\query.py", line 1511, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Project 911\Ecommerce\venv\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(~Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Project 911\Ecommerce\venv\Lib\site-packages\django\db\models\sql\query.py", line 1646, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Project 911\Ecommerce\venv\Lib\site-packages\django\db\models\sql\query.py", line 1678, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\Project 911\Ecommerce\venv\Lib\site-packages\django\db\models\sql\query.py", line 1526, in build_filter
    lookups, parts, reffed_expression = self.solve_lookup_type(arg, summarize)
                                        ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Project 911\Ecommerce\venv\Lib\site-packages\django\db\models\sql\query.py", line 1333, in solve_lookup_type
    _, field, _, lookup_parts = self.names_to_path(lookup_splitted, self.get_meta())
                                ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Project 911\Ecommerce\venv\Lib\site-packages\django\db\models\sql\query.py", line 1805, in names_to_path
    raise FieldError(
    ...<2 lines>...
    )
django.core.exceptions.FieldError: Cannot resolve keyword 'behaviors' into field. Choices are: brand, cartitem, category, category_id, compare_price, created_at, description, id, image, is_active, is_featured, name, orderitem, price, ratings, recommendation, short_description, sku, slug, stock_quantity, tags, updated_at, userbehavior
"GET /api/recommendations/personalized/ HTTP/1.1" 500 121399
"OPTIONS /api/orders/cart/ HTTP/1.1" 200 0
"GET /api/orders/cart/ HTTP/1.1" 200 65
"GET /api/products/ HTTP/1.1" 200 2494
"POST /api/auth/behavior/ HTTP/1.1" 200 53
"POST /api/auth/behavior/ HTTP/1.1" 200 53
"POST /api/auth/behavior/ HTTP/1.1" 200 53
"POST /api/auth/behavior/ HTTP/1.1" 200 54
"POST /api/auth/behavior/ HTTP/1.1" 200 54
"POST /api/auth/behavior/ HTTP/1.1" 200 54
"POST /api/orders/cart/add/ HTTP/1.1" 200 112
"GET /api/orders/cart/ HTTP/1.1" 200 348
"POST /api/auth/behavior/ HTTP/1.1" 200 54
"POST /api/orders/cart/add/ HTTP/1.1" 200 107
"GET /api/orders/cart/ HTTP/1.1" 200 621
"POST /api/auth/behavior/ HTTP/1.1" 200 54
"GET /api/orders/cart/ HTTP/1.1" 200 621
"GET /api/orders/cart/ HTTP/1.1" 200 621
"GET /api/orders/cart/ HTTP/1.1" 200 621
"OPTIONS /api/orders/cart/remove/1/ HTTP/1.1" 200 0
"DELETE /api/orders/cart/remove/1/ HTTP/1.1" 200 58
"GET /api/orders/cart/ HTTP/1.1" 200 340
"GET /api/orders/cart/ HTTP/1.1" 200 340
"OPTIONS /api/orders/cart/remove/2/ HTTP/1.1" 200 0
"DELETE /api/orders/cart/remove/2/ HTTP/1.1" 200 52
"GET /api/orders/cart/ HTTP/1.1" 200 65
"GET /api/orders/cart/ HTTP/1.1" 200 65
