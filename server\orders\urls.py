from django.urls import path
from .views import (
    get_cart, add_to_cart, update_cart_item, remove_from_cart, clear_cart,
    create_order, user_orders, order_detail
)

urlpatterns = [
    # Cart endpoints
    path('cart/', get_cart, name='get-cart'),
    path('cart/add/', add_to_cart, name='add-to-cart'),
    path('cart/update/<int:item_id>/', update_cart_item, name='update-cart-item'),
    path('cart/remove/<int:item_id>/', remove_from_cart, name='remove-from-cart'),
    path('cart/clear/', clear_cart, name='clear-cart'),

    # Order endpoints
    path('create/', create_order, name='create-order'),
    path('', user_orders, name='user-orders'),
    path('<uuid:order_id>/', order_detail, name='order-detail'),
]
