from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator

User = get_user_model()


class Recommendation(models.Model):
    """Store AI-generated recommendations for users"""
    RECOMMENDATION_TYPES = [
        ('collaborative', 'Collaborative Filtering'),
        ('content_based', 'Content-Based Filtering'),
        ('hybrid', 'Hybrid Recommendation'),
        ('trending', 'Trending Products'),
        ('similar', 'Similar Products'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='recommendations')
    product = models.ForeignKey('products.Product', on_delete=models.CASCADE)
    recommendation_type = models.CharField(max_length=20, choices=RECOMMENDATION_TYPES)
    score = models.FloatField(validators=[MinValueValidator(0), MaxValueValidator(1)])
    reason = models.TextField(blank=True)
    is_active = models.<PERSON><PERSON><PERSON><PERSON><PERSON>(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['user', 'product', 'recommendation_type']
        ordering = ['-score', '-created_at']
    
    def __str__(self):
        return f"{self.user.email} - {self.product.name} ({self.recommendation_type})"
