<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { margin: 5px; padding: 10px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>E-commerce API Test</h1>
    
    <div>
        <button onclick="testProducts()">Test Products API</button>
        <button onclick="testCategories()">Test Categories API</button>
        <button onclick="testTrending()">Test Trending API</button>
        <button onclick="testFeatured()">Test Featured API</button>
        <button onclick="testRegister()">Test Register</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="results"></div>

    <script>
        const API_BASE_URL = 'http://127.0.0.1:8000/api';
        
        function addResult(title, success, data) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${success ? 'success' : 'error'}`;
            div.innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            results.appendChild(div);
        }
        
        async function testProducts() {
            try {
                const response = await fetch(`${API_BASE_URL}/products/`);
                const data = await response.json();
                addResult('Products API', response.ok, data);
            } catch (error) {
                addResult('Products API', false, { error: error.message });
            }
        }
        
        async function testCategories() {
            try {
                const response = await fetch(`${API_BASE_URL}/products/categories/`);
                const data = await response.json();
                addResult('Categories API', response.ok, data);
            } catch (error) {
                addResult('Categories API', false, { error: error.message });
            }
        }
        
        async function testTrending() {
            try {
                const response = await fetch(`${API_BASE_URL}/recommendations/trending/`);
                const data = await response.json();
                addResult('Trending API', response.ok, data);
            } catch (error) {
                addResult('Trending API', false, { error: error.message });
            }
        }
        
        async function testFeatured() {
            try {
                const response = await fetch(`${API_BASE_URL}/products/featured/`);
                const data = await response.json();
                addResult('Featured API', response.ok, data);
            } catch (error) {
                addResult('Featured API', false, { error: error.message });
            }
        }
        
        async function testRegister() {
            try {
                const testUser = {
                    first_name: 'Test',
                    last_name: 'User',
                    username: 'testuser' + Date.now(),
                    email: 'test' + Date.now() + '@example.com',
                    password: 'testpassword123'
                };
                
                const response = await fetch(`${API_BASE_URL}/auth/register/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify(testUser)
                });
                
                const data = await response.json();
                addResult('Register API', response.ok, data);
            } catch (error) {
                addResult('Register API', false, { error: error.message });
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
