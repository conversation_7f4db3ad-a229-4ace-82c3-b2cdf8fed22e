#!/bin/bash

echo "========================================"
echo "E-commerce Recommendation System"
echo "Development Server Starter"
echo "========================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed"
    echo "Please install Python 3 and try again"
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Error: Virtual environment not found"
    echo "Please create a virtual environment first:"
    echo "  python3 -m venv venv"
    exit 1
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Check if Django is installed
python -c "import django" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Error: Django is not installed"
    echo "Installing dependencies..."
    cd server
    pip install -r requirements.txt
    cd ..
fi

# Start the development server
echo
echo "Starting development servers..."
echo "Backend: http://127.0.0.1:8000"
echo "Frontend: http://localhost:3000"
echo
echo "Press Ctrl+C to stop both servers"
echo

python3 start_dev_server.py
