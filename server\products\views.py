from rest_framework import generics, filters, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from .models import Category, Product, ProductRating
from .serializers import (
    CategorySerializer, ProductSerializer, ProductListSerializer, 
    ProductRatingSerializer
)


class CategoryListView(generics.ListAPIView):
    """List all categories"""
    queryset = Category.objects.filter(is_active=True)
    serializer_class = CategorySerializer
    permission_classes = [permissions.AllowAny]


class ProductListView(generics.ListAPIView):
    """List all products with filtering and search"""
    queryset = Product.objects.filter(is_active=True)
    serializer_class = ProductListSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category', 'is_featured', 'brand']
    search_fields = ['name', 'description', 'brand', 'tags']
    ordering_fields = ['price', 'created_at', 'name']
    ordering = ['-created_at']


class ProductDetailView(generics.RetrieveAPIView):
    """Get product details"""
    queryset = Product.objects.filter(is_active=True)
    serializer_class = ProductSerializer
    permission_classes = [permissions.AllowAny]
    lookup_field = 'slug'


class FeaturedProductsView(generics.ListAPIView):
    """List featured products"""
    queryset = Product.objects.filter(is_active=True, is_featured=True)
    serializer_class = ProductListSerializer
    permission_classes = [permissions.AllowAny]


class ProductRatingListCreateView(generics.ListCreateAPIView):
    """List and create product ratings"""
    serializer_class = ProductRatingSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    
    def get_queryset(self):
        product_id = self.kwargs.get('product_id')
        return ProductRating.objects.filter(product_id=product_id, is_approved=True)
    
    def perform_create(self, serializer):
        product_id = self.kwargs.get('product_id')
        serializer.save(user=self.request.user, product_id=product_id)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def product_search(request):
    """Search products"""
    query = request.GET.get('q', '')
    category = request.GET.get('category', '')
    
    products = Product.objects.filter(is_active=True)
    
    if query:
        products = products.filter(name__icontains=query)
    
    if category:
        products = products.filter(category__slug=category)
    
    serializer = ProductListSerializer(products[:20], many=True)
    return Response(serializer.data)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def create_sample_data(request):
    """Create sample products and categories for testing"""
    try:
        # Create categories
        categories_data = [
            {'name': 'Electronics', 'description': 'Electronic devices and gadgets'},
            {'name': 'Clothing', 'description': 'Fashion and apparel'},
            {'name': 'Books', 'description': 'Books and literature'},
            {'name': 'Home & Garden', 'description': 'Home improvement and gardening'},
            {'name': 'Sports', 'description': 'Sports and fitness equipment'},
        ]

        categories = []
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )
            categories.append(category)

        # Create sample products
        products_data = [
            {
                'name': 'Wireless Bluetooth Headphones',
                'description': 'High-quality wireless headphones with noise cancellation',
                'category': categories[0],  # Electronics
                'price': 99.99,
                'is_featured': True,
                'image': 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500',
                'stock_quantity': 50
            },
            {
                'name': 'Cotton T-Shirt',
                'description': 'Comfortable 100% cotton t-shirt in various colors',
                'category': categories[1],  # Clothing
                'price': 19.99,
                'is_featured': True,
                'image': 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500',
                'stock_quantity': 100
            },
            {
                'name': 'Programming Book',
                'description': 'Learn Python programming from scratch',
                'category': categories[2],  # Books
                'price': 29.99,
                'is_featured': False,
                'image': 'https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=500',
                'stock_quantity': 25
            },
            {
                'name': 'Garden Tools Set',
                'description': 'Complete set of essential gardening tools',
                'category': categories[3],  # Home & Garden
                'price': 49.99,
                'is_featured': True,
                'image': 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=500',
                'stock_quantity': 30
            },
            {
                'name': 'Yoga Mat',
                'description': 'Non-slip yoga mat for all fitness levels',
                'category': categories[4],  # Sports
                'price': 24.99,
                'is_featured': False,
                'image': 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=500',
                'stock_quantity': 75
            }
        ]

        created_products = []
        for prod_data in products_data:
            product, created = Product.objects.get_or_create(
                name=prod_data['name'],
                defaults=prod_data
            )
            created_products.append(product)

        return Response({
            'message': f'Created {len(categories)} categories and {len(created_products)} products',
            'categories': [cat.name for cat in categories],
            'products': [prod.name for prod in created_products]
        })

    except Exception as e:
        return Response({'error': str(e)}, status=500)
