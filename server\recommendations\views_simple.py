"""
Simple Django views for recommendations without DRF dependencies
"""
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from products.models import Product
from .models import Recommendation


def trending_products(request):
    """Get trending products"""
    # Simple trending logic - most recently added products
    products = Product.objects.filter(is_active=True).order_by('-created_at')[:10]
    
    products_data = []
    for product in products:
        products_data.append({
            'id': product.id,
            'name': product.name,
            'slug': product.slug,
            'description': product.short_description or product.description[:200],
            'category': product.category.name,
            'price': str(product.price),
            'image': product.image,
            'average_rating': product.average_rating,
            'rating_count': product.rating_count,
            'is_in_stock': product.is_in_stock,
        })
    
    return JsonResponse({
        'type': 'trending',
        'count': len(products_data),
        'products': products_data
    })


@login_required
def personalized_recommendations(request):
    """Get personalized recommendations for user"""
    user = request.user
    rec_type = request.GET.get('type', 'hybrid')
    limit = int(request.GET.get('limit', 10))
    
    # Simple recommendation logic - products from categories user has viewed
    from accounts.models import UserBehavior
    
    # Get categories user has interacted with
    user_categories = UserBehavior.objects.filter(
        user=user,
        product__isnull=False
    ).values_list('product__category', flat=True).distinct()
    
    if user_categories:
        # Get products from those categories
        products = Product.objects.filter(
            category__in=user_categories,
            is_active=True
        ).exclude(
            behaviors__user=user,
            behaviors__action='purchase'
        ).order_by('-created_at')[:limit]
    else:
        # Fallback to trending products
        products = Product.objects.filter(is_active=True).order_by('-created_at')[:limit]
    
    products_data = []
    for product in products:
        products_data.append({
            'id': product.id,
            'name': product.name,
            'slug': product.slug,
            'description': product.short_description or product.description[:200],
            'category': product.category.name,
            'price': str(product.price),
            'image': product.image,
            'average_rating': product.average_rating,
            'rating_count': product.rating_count,
            'is_in_stock': product.is_in_stock,
        })
    
    return JsonResponse({
        'type': rec_type,
        'count': len(products_data),
        'products': products_data
    })


@login_required
def user_recommendations(request):
    """Get stored recommendations for user"""
    user = request.user
    rec_type = request.GET.get('type', 'hybrid')
    
    recommendations = Recommendation.objects.filter(
        user=user,
        is_active=True,
        recommendation_type=rec_type
    ).select_related('product')[:10]
    
    recommendations_data = []
    for rec in recommendations:
        product = rec.product
        recommendations_data.append({
            'id': rec.id,
            'score': rec.score,
            'reason': rec.reason,
            'product': {
                'id': product.id,
                'name': product.name,
                'slug': product.slug,
                'description': product.short_description or product.description[:200],
                'category': product.category.name,
                'price': str(product.price),
                'image': product.image,
                'average_rating': product.average_rating,
                'rating_count': product.rating_count,
                'is_in_stock': product.is_in_stock,
            }
        })
    
    return JsonResponse({
        'type': rec_type,
        'count': len(recommendations_data),
        'recommendations': recommendations_data
    })
