"""
Simple Django views for recommendations without DRF dependencies
"""
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from functools import wraps
from products.models import Product
from .models import Recommendation


def ajax_login_required(view_func):
    """
    Decorator for views that checks that the user is logged in.
    Uses custom token authentication as fallback for session issues.
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        # First try Django session authentication
        if request.user.is_authenticated:
            return view_func(request, *args, **kwargs)

        # Fallback to custom token authentication
        auth_token = request.headers.get('X-Auth-Token') or request.COOKIES.get('auth_token')
        if auth_token:
            # Import here to avoid circular imports
            from accounts.views_simple import get_user_from_token
            user = get_user_from_token(auth_token)
            if user:
                request.user = user
                return view_func(request, *args, **kwargs)

        # No valid authentication found
        return JsonResponse({'error': 'Authentication required'}, status=401)

    return _wrapped_view


def trending_products(request):
    """Get trending products"""
    # Simple trending logic - most recently added products
    products = Product.objects.filter(is_active=True).order_by('-created_at')[:10]
    
    products_data = []
    for product in products:
        products_data.append({
            'id': product.id,
            'name': product.name,
            'slug': product.slug,
            'description': product.short_description or product.description[:200],
            'category': product.category.name,
            'price': str(product.price),
            'image': product.image,
            'average_rating': product.average_rating,
            'rating_count': product.rating_count,
            'is_in_stock': product.is_in_stock,
        })
    
    return JsonResponse({
        'type': 'trending',
        'count': len(products_data),
        'products': products_data
    })


@ajax_login_required
def personalized_recommendations(request):
    """Get personalized recommendations for user"""
    try:
        user = request.user
        rec_type = request.GET.get('type', 'hybrid')
        limit = int(request.GET.get('limit', 10))

        print(f"DEBUG: Getting recommendations for user: {user}")

        # Simple fallback to featured/trending products for now
        products = Product.objects.filter(is_active=True).order_by('-created_at')[:limit]

        print(f"DEBUG: Found {products.count()} products")

        products_data = []
        for product in products:
            products_data.append({
                'id': product.id,
                'name': product.name,
                'slug': product.slug,
                'description': product.short_description or product.description[:200],
                'category': product.category.name if product.category else 'Uncategorized',
                'price': str(product.price),
                'image': product.image,
                'average_rating': getattr(product, 'average_rating', 0),
                'rating_count': getattr(product, 'rating_count', 0),
                'is_in_stock': getattr(product, 'is_in_stock', True),
            })

        return JsonResponse({
            'type': rec_type,
            'count': len(products_data),
            'products': products_data
        })

    except Exception as e:
        print(f"DEBUG: Error in personalized_recommendations: {e}")
        return JsonResponse({
            'type': 'error',
            'count': 0,
            'products': [],
            'error': str(e)
        })


@ajax_login_required
def user_recommendations(request):
    """Get stored recommendations for user"""
    user = request.user
    rec_type = request.GET.get('type', 'hybrid')
    
    recommendations = Recommendation.objects.filter(
        user=user,
        is_active=True,
        recommendation_type=rec_type
    ).select_related('product')[:10]
    
    recommendations_data = []
    for rec in recommendations:
        product = rec.product
        recommendations_data.append({
            'id': rec.id,
            'score': rec.score,
            'reason': rec.reason,
            'product': {
                'id': product.id,
                'name': product.name,
                'slug': product.slug,
                'description': product.short_description or product.description[:200],
                'category': product.category.name,
                'price': str(product.price),
                'image': product.image,
                'average_rating': product.average_rating,
                'rating_count': product.rating_count,
                'is_in_stock': product.is_in_stock,
            }
        })
    
    return JsonResponse({
        'type': rec_type,
        'count': len(recommendations_data),
        'recommendations': recommendations_data
    })
