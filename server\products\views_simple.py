"""
Simple Django views without DRF dependencies
"""
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.core.paginator import Paginator
from django.shortcuts import get_object_or_404
import json
from .models import Category, Product, ProductRating


def product_list(request):
    """List all products"""
    products = Product.objects.filter(is_active=True).select_related('category')
    
    # Simple search
    search = request.GET.get('search', '')
    if search:
        products = products.filter(name__icontains=search)
    
    # Category filter
    category = request.GET.get('category', '')
    if category:
        products = products.filter(category__slug=category)
    
    # Pagination
    paginator = Paginator(products, 20)
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)
    
    # Convert to JSON
    products_data = []
    for product in page_obj:
        products_data.append({
            'id': product.id,
            'name': product.name,
            'slug': product.slug,
            'description': product.short_description or product.description[:200],
            'category': product.category.name,
            'price': str(product.price),
            'compare_price': str(product.compare_price) if product.compare_price else None,
            'image': product.image,
            'is_featured': product.is_featured,
            'average_rating': product.average_rating,
            'rating_count': product.rating_count,
            'is_in_stock': product.is_in_stock,
            'discount_percentage': product.discount_percentage,
        })
    
    return JsonResponse({
        'count': paginator.count,
        'num_pages': paginator.num_pages,
        'current_page': page_obj.number,
        'results': products_data
    })


def product_detail(request, slug):
    """Get product details"""
    product = get_object_or_404(Product, slug=slug, is_active=True)
    
    data = {
        'id': product.id,
        'name': product.name,
        'slug': product.slug,
        'description': product.description,
        'short_description': product.short_description,
        'category': {
            'id': product.category.id,
            'name': product.category.name,
            'slug': product.category.slug,
        },
        'price': str(product.price),
        'compare_price': str(product.compare_price) if product.compare_price else None,
        'sku': product.sku,
        'stock_quantity': product.stock_quantity,
        'image': product.image,
        'brand': product.brand,
        'tags': product.tags,
        'is_featured': product.is_featured,
        'average_rating': product.average_rating,
        'rating_count': product.rating_count,
        'is_in_stock': product.is_in_stock,
        'discount_percentage': product.discount_percentage,
        'created_at': product.created_at.isoformat(),
    }
    
    return JsonResponse(data)


def category_list(request):
    """List all categories"""
    categories = Category.objects.filter(is_active=True)
    
    categories_data = []
    for category in categories:
        categories_data.append({
            'id': category.id,
            'name': category.name,
            'slug': category.slug,
            'description': category.description,
            'image': category.image,
            'product_count': category.products.filter(is_active=True).count(),
        })
    
    return JsonResponse({'results': categories_data})


def featured_products(request):
    """Get featured products"""
    products = Product.objects.filter(is_active=True, is_featured=True)[:10]
    
    products_data = []
    for product in products:
        products_data.append({
            'id': product.id,
            'name': product.name,
            'slug': product.slug,
            'description': product.short_description or product.description[:200],
            'category': product.category.name,
            'price': str(product.price),
            'compare_price': str(product.compare_price) if product.compare_price else None,
            'image': product.image,
            'average_rating': product.average_rating,
            'rating_count': product.rating_count,
            'is_in_stock': product.is_in_stock,
            'discount_percentage': product.discount_percentage,
        })
    
    return JsonResponse({'results': products_data})


def trending_products(request):
    """Get trending products"""
    # Simple trending logic - most recently added products
    products = Product.objects.filter(is_active=True).order_by('-created_at')[:10]
    
    products_data = []
    for product in products:
        products_data.append({
            'id': product.id,
            'name': product.name,
            'slug': product.slug,
            'description': product.short_description or product.description[:200],
            'category': product.category.name,
            'price': str(product.price),
            'image': product.image,
            'average_rating': product.average_rating,
            'rating_count': product.rating_count,
        })
    
    return JsonResponse({'results': products_data})
