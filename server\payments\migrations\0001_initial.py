# Generated by Django 5.2.4 on 2025-07-16 08:33

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('orders', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentWebhook',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('charge.success', 'Charge Success'), ('charge.failed', 'Charge Failed'), ('transfer.success', 'Transfer Success'), ('transfer.failed', 'Transfer Failed')], max_length=50)),
                ('event_id', models.CharField(max_length=100, unique=True)),
                ('reference', models.CharField(blank=True, max_length=100)),
                ('data', models.<PERSON><PERSON><PERSON><PERSON>(default=dict)),
                ('processed', models.<PERSON><PERSON>anField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['event_type'], name='payments_pa_event_t_c5f758_idx'), models.Index(fields=['reference'], name='payments_pa_referen_c2929f_idx'), models.Index(fields=['processed'], name='payments_pa_process_a23dd1_idx')],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('currency', models.CharField(default='NGN', max_length=3)),
                ('payment_method', models.CharField(choices=[('card', 'Credit/Debit Card'), ('bank_transfer', 'Bank Transfer'), ('paystack', 'Paystack')], default='paystack', max_length=20)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('success', 'Success'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('paystack_reference', models.CharField(blank=True, max_length=100, unique=True)),
                ('paystack_access_code', models.CharField(blank=True, max_length=100)),
                ('paystack_response', models.JSONField(blank=True, default=dict)),
                ('transaction_fee', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('net_amount', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('failure_reason', models.TextField(blank=True)),
                ('notes', models.TextField(blank=True)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='orders.order')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'status'], name='payments_pa_user_id_01767a_idx'), models.Index(fields=['order', 'status'], name='payments_pa_order_i_a76289_idx'), models.Index(fields=['paystack_reference'], name='payments_pa_paystac_25e13d_idx'), models.Index(fields=['created_at'], name='payments_pa_created_b8a300_idx')],
            },
        ),
    ]
