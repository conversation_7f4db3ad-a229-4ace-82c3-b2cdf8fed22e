#!/usr/bin/env python3
"""
Development server starter for E-commerce Recommendation System
This script starts both the Django backend and a simple HTTP server for the frontend
"""

import os
import sys
import time
import subprocess
import threading
import webbrowser
from http.server import HTTPServer, SimpleHTTPRequestHandler
import socketserver
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))

class CORSHTTPRequestHandler(SimpleHTTPRequestHandler):
    """HTTP Request Handler with CORS support"""
    
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-CSRFToken')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

def start_frontend_server():
    """Start the frontend HTTP server"""
    os.chdir(os.path.join(ROOT_DIR, 'client'))
    
    PORT = 3000
    
    # Try to find an available port
    for port in range(3000, 3010):
        try:
            with socketserver.TCPServer(("", port), CORSHTTPRequestHandler) as httpd:
                PORT = port
                break
        except OSError:
            continue
    
    print(f"🌐 Starting frontend server on http://localhost:{PORT}")
    
    try:
        with socketserver.TCPServer(("", PORT), CORSHTTPRequestHandler) as httpd:
            print(f"✅ Frontend server running at http://localhost:{PORT}")
            
            # Open browser after a short delay
            def open_browser():
                time.sleep(2)
                webbrowser.open(f'http://localhost:{PORT}')
                print(f"🚀 Browser opened at http://localhost:{PORT}")
            
            threading.Thread(target=open_browser, daemon=True).start()
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Frontend server stopped")
    except Exception as e:
        print(f"❌ Error starting frontend server: {e}")

def start_backend_server():
    """Start the Django backend server"""
    os.chdir(os.path.join(ROOT_DIR, 'server'))
    
    print("🔧 Starting Django backend server...")
    
    try:
        # Start Django development server
        process = subprocess.Popen([
            sys.executable, 'manage.py', 'runserver', '127.0.0.1:8000'
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        
        print("✅ Django backend server starting on http://127.0.0.1:8000")
        
        # Monitor the process output
        for line in process.stdout:
            print(f"[Django] {line.strip()}")
            if "Starting development server" in line:
                print("🚀 Django server is ready!")
                
    except KeyboardInterrupt:
        print("\n🛑 Backend server stopped")
        process.terminate()
    except Exception as e:
        print(f"❌ Error starting backend server: {e}")

def main():
    """Main function to start both servers"""
    print("🎯 E-commerce Recommendation System - Development Server")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not os.path.exists('client') or not os.path.exists('server'):
        print("❌ Error: Please run this script from the project root directory")
        print("   Make sure both 'client' and 'server' folders exist")
        return
    
    try:
        # Start backend server in a separate thread
        backend_thread = threading.Thread(target=start_backend_server, daemon=True)
        backend_thread.start()
        
        # Wait a moment for backend to start
        time.sleep(3)
        
        # Start frontend server (this will block)
        start_frontend_server()
        
    except KeyboardInterrupt:
        print("\n🛑 Shutting down development servers...")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
