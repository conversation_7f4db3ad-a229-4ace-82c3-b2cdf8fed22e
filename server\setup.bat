@echo off
echo Setting up E-commerce Backend...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo Python found!

REM Try to install Django
echo Installing Django...
python -m pip install Django
if errorlevel 1 (
    echo Failed to install Django
    echo You may need to run: pip install Django
    pause
    exit /b 1
)

REM Run setup script
echo Running setup script...
python setup.py

echo.
echo Setup completed! You can now run:
echo python manage.py runserver
echo.
pause
