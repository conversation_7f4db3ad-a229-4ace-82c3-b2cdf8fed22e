from rest_framework import serializers
from .models import Recommendation
from products.serializers import ProductListSerializer


class RecommendationSerializer(serializers.ModelSerializer):
    """Serializer for recommendations"""
    product = ProductListSerializer(read_only=True)
    
    class Meta:
        model = Recommendation
        fields = ['id', 'product', 'recommendation_type', 'score', 'reason', 'created_at']
        read_only_fields = ['id', 'created_at']
