#!/usr/bin/env python
"""
Script to seed the database with sample data
Run this script after running migrations: python manage.py shell < seed_data.py
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ecommerce_backend.settings')
django.setup()

from products.models import Category, Product
from django.contrib.auth import get_user_model

User = get_user_model()

def create_categories():
    """Create sample categories"""
    categories_data = [
        {'name': 'Electronics', 'description': 'Electronic devices and gadgets'},
        {'name': 'Fashion', 'description': 'Clothing and accessories'},
        {'name': 'Food', 'description': 'Food and beverages'},
        {'name': 'Lifestyle', 'description': 'Lifestyle and home products'},
    ]
    
    for cat_data in categories_data:
        category, created = Category.objects.get_or_create(
            name=cat_data['name'],
            defaults=cat_data
        )
        if created:
            print(f"Created category: {category.name}")

def create_products():
    """Create sample products"""
    # Get categories
    electronics = Category.objects.get(name='Electronics')
    fashion = Category.objects.get(name='Fashion')
    food = Category.objects.get(name='Food')
    lifestyle = Category.objects.get(name='Lifestyle')
    
    products_data = [
        {
            'name': 'Premium Wireless Headphones',
            'description': 'High-quality wireless headphones with active noise cancellation technology. Perfect for music lovers and professionals alike.',
            'short_description': 'Premium wireless headphones with noise cancellation',
            'category': electronics,
            'price': 299.99,
            'compare_price': 399.99,
            'stock_quantity': 50,
            'image': 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
            'brand': 'AudioTech',
            'is_featured': True,
            'tags': ['wireless', 'headphones', 'noise-cancellation', 'premium']
        },
        {
            'name': 'Smart Fitness Watch',
            'description': 'Track your fitness goals with precision using this advanced smartwatch. Monitor heart rate, steps, and more.',
            'short_description': 'Advanced fitness tracking smartwatch',
            'category': electronics,
            'price': 199.99,
            'compare_price': 249.99,
            'stock_quantity': 30,
            'image': 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
            'brand': 'FitTech',
            'is_featured': True,
            'tags': ['smartwatch', 'fitness', 'health', 'tracking']
        },
        {
            'name': 'Minimalist Backpack',
            'description': 'Stylish and functional backpack for everyday use. Perfect for work, travel, or casual outings.',
            'short_description': 'Stylish minimalist backpack for daily use',
            'category': fashion,
            'price': 89.99,
            'stock_quantity': 25,
            'image': 'https://images.unsplash.com/photo-**********-98eeb64c6a62?w=400&h=400&fit=crop',
            'brand': 'UrbanStyle',
            'is_featured': False,
            'tags': ['backpack', 'minimalist', 'travel', 'work']
        },
        {
            'name': 'Organic Coffee Beans',
            'description': 'Premium organic coffee beans from sustainable farms. Rich flavor and ethically sourced.',
            'short_description': 'Premium organic coffee beans',
            'category': food,
            'price': 24.99,
            'stock_quantity': 100,
            'image': 'https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=400&h=400&fit=crop',
            'brand': 'EthicalBrew',
            'is_featured': True,
            'tags': ['coffee', 'organic', 'sustainable', 'premium']
        },
        {
            'name': 'Wireless Charging Pad',
            'description': 'Fast wireless charging for all compatible devices. Sleek design with LED indicators.',
            'short_description': 'Fast wireless charging pad',
            'category': electronics,
            'price': 49.99,
            'stock_quantity': 40,
            'image': 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=400&fit=crop',
            'brand': 'ChargeTech',
            'is_featured': False,
            'tags': ['wireless', 'charging', 'fast', 'sleek']
        },
        {
            'name': 'Eco-Friendly Water Bottle',
            'description': 'Sustainable stainless steel water bottle. Keeps drinks cold for 24h, hot for 12h.',
            'short_description': 'Sustainable stainless steel water bottle',
            'category': lifestyle,
            'price': 34.99,
            'stock_quantity': 60,
            'image': 'https://images.unsplash.com/photo-1602143407151-7111542de6e8?w=400&h=400&fit=crop',
            'brand': 'EcoLife',
            'is_featured': False,
            'tags': ['eco-friendly', 'water-bottle', 'sustainable', 'insulated']
        }
    ]
    
    for product_data in products_data:
        product, created = Product.objects.get_or_create(
            name=product_data['name'],
            defaults=product_data
        )
        if created:
            print(f"Created product: {product.name}")

def main():
    """Main function to seed data"""
    print("Starting data seeding...")
    
    create_categories()
    create_products()
    
    print("Data seeding completed!")
    print(f"Categories: {Category.objects.count()}")
    print(f"Products: {Product.objects.count()}")

if __name__ == "__main__":
    main()
