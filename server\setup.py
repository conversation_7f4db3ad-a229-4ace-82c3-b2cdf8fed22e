#!/usr/bin/env python3
"""
Setup script for the e-commerce backend
This script will try to set up the project with minimal dependencies
"""

import os
import sys
import subprocess
import sqlite3

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"Output: {e.stdout}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False

def check_python():
    """Check Python version"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ is required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def install_django():
    """Try to install Django"""
    try:
        import django
        print(f"✅ Django {django.get_version()} already installed")
        return True
    except ImportError:
        print("🔄 Installing Django...")
        return run_command(f"{sys.executable} -m pip install Django", "Django installation")

def setup_database():
    """Setup SQLite database"""
    db_path = "db.sqlite3"
    if os.path.exists(db_path):
        print(f"✅ Database {db_path} already exists")
        return True
    
    try:
        # Create empty database
        conn = sqlite3.connect(db_path)
        conn.close()
        print(f"✅ Created SQLite database: {db_path}")
        return True
    except Exception as e:
        print(f"❌ Failed to create database: {e}")
        return False

def run_migrations():
    """Run Django migrations"""
    commands = [
        ("python manage.py makemigrations", "Creating migrations"),
        ("python manage.py migrate", "Running migrations"),
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    return True

def create_superuser():
    """Create Django superuser"""
    print("\n🔄 Creating superuser...")
    print("You can skip this step and create it later with: python manage.py createsuperuser")
    
    try:
        username = input("Enter username (or press Enter to skip): ").strip()
        if not username:
            print("⏭️ Skipping superuser creation")
            return True
        
        email = input("Enter email: ").strip()
        password = input("Enter password: ").strip()
        
        command = f'echo "from django.contrib.auth import get_user_model; User = get_user_model(); User.objects.create_superuser(\'{username}\', \'{email}\', \'{password}\')" | python manage.py shell'
        return run_command(command, "Creating superuser")
    except KeyboardInterrupt:
        print("\n⏭️ Skipping superuser creation")
        return True

def seed_data():
    """Seed sample data"""
    if os.path.exists("seed_data.py"):
        return run_command("python manage.py shell < seed_data.py", "Seeding sample data")
    else:
        print("⏭️ No seed_data.py found, skipping data seeding")
        return True

def main():
    """Main setup function"""
    print("🚀 Setting up E-commerce Backend")
    print("=" * 50)
    
    # Check Python version
    if not check_python():
        sys.exit(1)
    
    # Try to install Django
    if not install_django():
        print("\n❌ Failed to install Django. Please install manually:")
        print("pip install Django")
        sys.exit(1)
    
    # Setup database
    if not setup_database():
        print("\n❌ Failed to setup database")
        sys.exit(1)
    
    # Run migrations
    if not run_migrations():
        print("\n❌ Failed to run migrations")
        sys.exit(1)
    
    # Create superuser (optional)
    create_superuser()
    
    # Seed data (optional)
    seed_data()
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("=" * 50)
    print("\nNext steps:")
    print("1. Run the development server: python manage.py runserver")
    print("2. Open your browser to: http://127.0.0.1:8000/")
    print("3. Access admin panel: http://127.0.0.1:8000/admin/")
    print("4. API endpoints: http://127.0.0.1:8000/api/")
    print("\nAPI Documentation:")
    print("- Products: /api/products/")
    print("- Authentication: /api/auth/")
    print("- Recommendations: /api/recommendations/")

if __name__ == "__main__":
    main()
