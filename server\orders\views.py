"""
Order and cart management views
"""
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404
from django.db import transaction
from functools import wraps
import json
from products.models import Product
from .models import Cart, CartItem, Order, OrderItem


def ajax_login_required(view_func):
    """
    Decorator for views that checks that the user is logged in, redirecting
    to the log-in page if necessary. For AJAX requests, returns JSON error.
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if request.user.is_authenticated:
            return view_func(request, *args, **kwargs)

        # Check if it's an AJAX request
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest' or \
           request.content_type == 'application/json' or \
           'application/json' in request.headers.get('Accept', ''):
            return JsonResponse({'error': 'Authentication required'}, status=401)

        # For non-AJAX requests, use the default login_required behavior
        return login_required(view_func)(request, *args, **kwargs)

    return _wrapped_view


@ajax_login_required
def get_cart(request):
    """Get user's cart"""
    cart, created = Cart.objects.get_or_create(user=request.user)
    
    cart_items = []
    for item in cart.items.all():
        cart_items.append({
            'id': item.id,
            'product': {
                'id': item.product.id,
                'name': item.product.name,
                'price': str(item.product.price),
                'image': item.product.image,
                'slug': item.product.slug,
            },
            'quantity': item.quantity,
            'unit_price': str(item.unit_price),
            'total_price': str(item.total_price),
        })
    
    return JsonResponse({
        'cart_id': cart.id,
        'items': cart_items,
        'total_items': cart.total_items,
        'total_price': str(cart.total_price),
    })


@csrf_exempt
@ajax_login_required
@require_http_methods(["POST"])
def add_to_cart(request):
    """Add item to cart"""
    try:
        data = json.loads(request.body)
        product_id = data.get('product_id')
        quantity = int(data.get('quantity', 1))
        
        if not product_id:
            return JsonResponse({'error': 'Product ID is required'}, status=400)
        
        if quantity <= 0:
            return JsonResponse({'error': 'Quantity must be positive'}, status=400)
        
        product = get_object_or_404(Product, id=product_id, is_active=True)
        
        # Check stock
        if product.stock_quantity < quantity:
            return JsonResponse({'error': 'Insufficient stock'}, status=400)
        
        cart, created = Cart.objects.get_or_create(user=request.user)
        
        # Check if item already exists in cart
        cart_item, created = CartItem.objects.get_or_create(
            cart=cart,
            product=product,
            defaults={'quantity': quantity}
        )
        
        if not created:
            # Update quantity
            cart_item.quantity += quantity
            cart_item.save()
        
        return JsonResponse({
            'message': f'{product.name} added to cart',
            'cart_item_id': cart_item.id,
            'quantity': cart_item.quantity,
            'total_price': str(cart_item.total_price),
        })
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except ValueError:
        return JsonResponse({'error': 'Invalid quantity'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@ajax_login_required
@require_http_methods(["PUT"])
def update_cart_item(request, item_id):
    """Update cart item quantity"""
    try:
        data = json.loads(request.body)
        quantity = int(data.get('quantity', 1))
        
        if quantity <= 0:
            return JsonResponse({'error': 'Quantity must be positive'}, status=400)
        
        cart_item = get_object_or_404(CartItem, id=item_id, cart__user=request.user)
        
        # Check stock
        if cart_item.product.stock_quantity < quantity:
            return JsonResponse({'error': 'Insufficient stock'}, status=400)
        
        cart_item.quantity = quantity
        cart_item.save()
        
        return JsonResponse({
            'message': 'Cart item updated',
            'quantity': cart_item.quantity,
            'total_price': str(cart_item.total_price),
        })
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except ValueError:
        return JsonResponse({'error': 'Invalid quantity'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@ajax_login_required
@require_http_methods(["DELETE"])
def remove_from_cart(request, item_id):
    """Remove item from cart"""
    try:
        cart_item = get_object_or_404(CartItem, id=item_id, cart__user=request.user)
        product_name = cart_item.product.name
        cart_item.delete()
        
        return JsonResponse({'message': f'{product_name} removed from cart'})
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@ajax_login_required
@require_http_methods(["DELETE"])
def clear_cart(request):
    """Clear all items from cart"""
    try:
        cart = get_object_or_404(Cart, user=request.user)
        cart.items.all().delete()
        
        return JsonResponse({'message': 'Cart cleared'})
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@ajax_login_required
@require_http_methods(["POST"])
def create_order(request):
    """Create order from cart"""
    try:
        data = json.loads(request.body)
        shipping_address = data.get('shipping_address', {})
        
        if not shipping_address:
            return JsonResponse({'error': 'Shipping address is required'}, status=400)
        
        cart = get_object_or_404(Cart, user=request.user)
        
        if not cart.items.exists():
            return JsonResponse({'error': 'Cart is empty'}, status=400)
        
        with transaction.atomic():
            # Create order
            order = Order.objects.create(
                user=request.user,
                subtotal=cart.total_price,
                total_amount=cart.total_price,  # Add tax/shipping calculation here
                shipping_address=shipping_address
            )
            
            # Create order items
            for cart_item in cart.items.all():
                OrderItem.objects.create(
                    order=order,
                    product=cart_item.product,
                    quantity=cart_item.quantity,
                    unit_price=cart_item.unit_price,
                    product_name=cart_item.product.name
                )
                
                # Update product stock
                cart_item.product.stock_quantity -= cart_item.quantity
                cart_item.product.save()
            
            # Clear cart
            cart.items.all().delete()
            
            return JsonResponse({
                'message': 'Order created successfully',
                'order_id': str(order.id),
                'order_number': order.order_number,
                'total_amount': str(order.total_amount),
            })
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@ajax_login_required
def user_orders(request):
    """Get user's orders"""
    orders = Order.objects.filter(user=request.user).order_by('-created_at')
    
    orders_data = []
    for order in orders:
        order_items = []
        for item in order.items.all():
            order_items.append({
                'product_name': item.product_name,
                'quantity': item.quantity,
                'unit_price': str(item.unit_price),
                'total_price': str(item.total_price),
            })
        
        orders_data.append({
            'id': str(order.id),
            'order_number': order.order_number,
            'status': order.status,
            'total_amount': str(order.total_amount),
            'created_at': order.created_at.isoformat(),
            'items': order_items,
            'item_count': order.item_count,
        })
    
    return JsonResponse({'orders': orders_data})


@ajax_login_required
def order_detail(request, order_id):
    """Get order details"""
    order = get_object_or_404(Order, id=order_id, user=request.user)
    
    order_items = []
    for item in order.items.all():
        order_items.append({
            'product_name': item.product_name,
            'quantity': item.quantity,
            'unit_price': str(item.unit_price),
            'total_price': str(item.total_price),
        })
    
    return JsonResponse({
        'id': str(order.id),
        'order_number': order.order_number,
        'status': order.status,
        'subtotal': str(order.subtotal),
        'tax_amount': str(order.tax_amount),
        'shipping_amount': str(order.shipping_amount),
        'total_amount': str(order.total_amount),
        'shipping_address': order.shipping_address,
        'created_at': order.created_at.isoformat(),
        'items': order_items,
        'item_count': order.item_count,
    })
