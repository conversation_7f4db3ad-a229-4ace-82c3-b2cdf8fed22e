# AI-Powered E-commerce Recommendation System

A full-stack e-commerce platform with AI-powered product recommendations using Django REST Framework for the backend and vanilla JavaScript for the frontend.

## Project Structure

```
├── client/                 # Frontend (Vanilla JavaScript)
│   ├── index.html         # Main HTML file
│   ├── script.js          # JavaScript functionality
│   └── styles.css         # CSS styles
├── server/                # Backend (Django REST Framework)
│   ├── manage.py          # Django management script
│   ├── requirements.txt   # Python dependencies
│   ├── .env              # Environment variables
│   ├── seed_data.py      # Database seeding script
│   ├── ecommerce_backend/ # Main Django project
│   ├── accounts/         # User authentication & profiles
│   ├── products/         # Product management
│   ├── orders/           # Shopping cart & orders
│   ├── recommendations/  # AI recommendation engine
│   └── payments/         # Payment processing (Paystack)
└── README.md
```

## Features

### Backend Features
- **User Authentication**: JWT-based authentication with registration, login, logout
- **Product Management**: CRUD operations for products and categories
- **Shopping Cart**: Add/remove items, quantity management
- **Order Management**: Order creation and tracking
- **AI Recommendations**: Collaborative filtering and content-based recommendations
- **User Behavior Tracking**: Track user interactions for ML training
- **Payment Integration**: Paystack payment gateway integration
- **Admin Panel**: Django admin for content management

### Frontend Features
- **Responsive Design**: Mobile-first responsive design
- **Product Catalog**: Browse products with filtering and search
- **Shopping Cart**: Interactive cart with real-time updates
- **User Authentication**: Login/register forms
- **Product Recommendations**: Display AI-powered recommendations
- **Modern UI**: Clean, modern interface with smooth animations

## Technology Stack

### Backend
- **Framework**: Django 4.2.7 with Django REST Framework
- **Database**: PostgreSQL
- **Authentication**: JWT (Simple JWT)
- **ML Libraries**: Scikit-learn, Pandas, NumPy
- **Payment**: Paystack API
- **Deployment**: Gunicorn, WhiteNoise

### Frontend
- **Languages**: HTML5, CSS3, JavaScript (ES6+)
- **Styling**: Custom CSS with CSS Grid and Flexbox
- **Icons**: Unicode emojis and symbols
- **Images**: Unsplash API for product images

## 🚀 Quick Start (Recommended)

### One-Click Development Server

**For Windows:**
```bash
# Double-click or run in terminal
start_dev.bat
```

**For Linux/Mac:**
```bash
# Make executable and run
chmod +x start_dev.sh
./start_dev.sh
```

**For Python (Any OS):**
```bash
python start_dev_server.py
```

This will automatically:
- ✅ Start the Django backend server on `http://127.0.0.1:8000`
- ✅ Start the frontend server on `http://localhost:3000`
- ✅ Open your browser automatically
- ✅ Enable CORS for API communication

## Setup Instructions

### Prerequisites
- Python 3.8+
- PostgreSQL
- Node.js (optional, for frontend development)

### Backend Setup

#### Quick Setup (Recommended)

1. **Navigate to server directory**
   ```bash
   cd server
   ```

2. **Run automatic setup**

   **Windows:**
   ```bash
   setup.bat
   ```

   **Mac/Linux:**
   ```bash
   python setup.py
   ```

3. **Start the server**
   ```bash
   python manage.py runserver
   ```

#### Manual Setup (If automatic setup fails)

1. **Install Django only**
   ```bash
   pip install Django
   ```

2. **Run migrations**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

3. **Create superuser (optional)**
   ```bash
   python manage.py createsuperuser
   ```

4. **Add sample data (optional)**
   ```bash
   python manage.py shell < seed_data.py
   ```

5. **Run development server**
   ```bash
   python manage.py runserver
   ```

#### Notes
- Uses SQLite database by default (no PostgreSQL setup required)
- Minimal dependencies to avoid installation issues
- Advanced ML features can be added later

### Frontend Setup

1. **Navigate to client directory**
   ```bash
   cd client
   ```

2. **Open in browser**
   - Open `index.html` in your browser, or
   - Use a local server like Live Server in VS Code

## API Endpoints

### Authentication
- `POST /api/auth/register/` - User registration
- `POST /api/auth/login/` - User login
- `POST /api/auth/logout/` - User logout
- `POST /api/auth/token/refresh/` - Refresh JWT token
- `GET /api/auth/profile/` - Get user profile
- `PUT /api/auth/profile/` - Update user profile

### Products
- `GET /api/products/` - List all products
- `GET /api/products/featured/` - List featured products
- `GET /api/products/categories/` - List categories
- `GET /api/products/<slug>/` - Get product details
- `GET /api/products/search/` - Search products

### Orders
- `GET /api/orders/cart/` - Get user's cart
- `POST /api/orders/cart/add/` - Add item to cart
- `PUT /api/orders/cart/update/` - Update cart item
- `DELETE /api/orders/cart/remove/` - Remove item from cart

### Recommendations
- `GET /api/recommendations/` - Get user recommendations
- `POST /api/recommendations/feedback/` - Submit recommendation feedback

## Development

### Running Tests
```bash
cd server
python manage.py test
```

### Code Style
- Backend: Follow PEP 8 guidelines
- Frontend: Use ESLint and Prettier for consistent formatting

### Database Migrations
```bash
python manage.py makemigrations
python manage.py migrate
```

## Deployment

### Backend Deployment (Heroku)
1. Install Heroku CLI
2. Create Heroku app
3. Set environment variables
4. Deploy using Git

### Frontend Deployment
- Deploy to Netlify, Vercel, or any static hosting service
- Update API endpoints to point to production backend

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support, email <EMAIL> or create an issue in the repository.
