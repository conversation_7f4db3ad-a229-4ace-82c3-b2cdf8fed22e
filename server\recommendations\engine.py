"""
Simple recommendation engine without heavy ML dependencies
Uses basic algorithms for product recommendations
"""

from django.db.models import Count, Q, Avg
from django.contrib.auth import get_user_model
from products.models import Product, ProductRating
from accounts.models import UserBehavior
from orders.models import OrderItem
from .models import Recommendation
import random
from collections import defaultdict, Counter

User = get_user_model()


class SimpleRecommendationEngine:
    """Simple recommendation engine using basic algorithms"""
    
    def __init__(self):
        self.min_score = 0.1
        self.max_recommendations = 10
    
    def get_user_recommendations(self, user, recommendation_type='hybrid', limit=10):
        """Get recommendations for a user"""
        if recommendation_type == 'collaborative':
            return self.collaborative_filtering(user, limit)
        elif recommendation_type == 'content_based':
            return self.content_based_filtering(user, limit)
        elif recommendation_type == 'trending':
            return self.trending_products(limit)
        elif recommendation_type == 'similar':
            return self.similar_products_based_on_behavior(user, limit)
        else:  # hybrid
            return self.hybrid_recommendations(user, limit)
    
    def collaborative_filtering(self, user, limit=10):
        """Simple collaborative filtering based on user behavior"""
        # Find users with similar behavior
        user_products = set(
            UserBehavior.objects.filter(
                user=user, 
                action__in=['view', 'purchase', 'add_to_cart']
            ).values_list('product_id', flat=True)
        )
        
        if not user_products:
            return self.trending_products(limit)
        
        # Find users who interacted with similar products
        similar_users = User.objects.filter(
            behaviors__product_id__in=user_products
        ).exclude(id=user.id).annotate(
            common_products=Count('behaviors__product_id')
        ).order_by('-common_products')[:20]
        
        # Get products these similar users liked
        recommended_products = Product.objects.filter(
            behaviors__user__in=similar_users,
            behaviors__action__in=['purchase', 'add_to_cart'],
            is_active=True
        ).exclude(
            id__in=user_products
        ).annotate(
            recommendation_score=Count('behaviors')
        ).order_by('-recommendation_score')[:limit]
        
        return self._create_recommendations(user, recommended_products, 'collaborative')
    
    def content_based_filtering(self, user, limit=10):
        """Content-based filtering using product attributes"""
        # Get user's preferred categories and brands
        user_interactions = UserBehavior.objects.filter(
            user=user,
            action__in=['view', 'purchase', 'add_to_cart'],
            product__isnull=False
        ).select_related('product')
        
        if not user_interactions.exists():
            return self.trending_products(limit)
        
        # Count preferences
        category_counts = Counter()
        brand_counts = Counter()
        tag_counts = Counter()
        
        for interaction in user_interactions:
            product = interaction.product
            category_counts[product.category_id] += 1
            if product.brand:
                brand_counts[product.brand] += 1
            for tag in product.tags:
                tag_counts[tag] += 1
        
        # Get top preferences
        top_categories = [cat_id for cat_id, _ in category_counts.most_common(3)]
        top_brands = [brand for brand, _ in brand_counts.most_common(3)]
        top_tags = [tag for tag, _ in tag_counts.most_common(5)]
        
        # Find products matching preferences
        q_objects = Q()
        if top_categories:
            q_objects |= Q(category_id__in=top_categories)
        if top_brands:
            q_objects |= Q(brand__in=top_brands)
        if top_tags:
            for tag in top_tags:
                q_objects |= Q(tags__contains=[tag])
        
        recommended_products = Product.objects.filter(
            q_objects,
            is_active=True
        ).exclude(
            behaviors__user=user,
            behaviors__action__in=['view', 'purchase']
        ).annotate(
            avg_rating=Avg('ratings__rating')
        ).order_by('-avg_rating', '-created_at')[:limit]
        
        return self._create_recommendations(user, recommended_products, 'content_based')
    
    def trending_products(self, limit=10):
        """Get trending products based on recent activity"""
        from django.utils import timezone
        from datetime import timedelta
        
        # Products with most activity in last 7 days
        week_ago = timezone.now() - timedelta(days=7)
        
        trending = Product.objects.filter(
            is_active=True,
            behaviors__timestamp__gte=week_ago
        ).annotate(
            activity_count=Count('behaviors'),
            avg_rating=Avg('ratings__rating')
        ).order_by('-activity_count', '-avg_rating')[:limit]
        
        return list(trending)
    
    def similar_products_based_on_behavior(self, user, limit=10):
        """Find products similar to what user has interacted with"""
        # Get products user has interacted with
        user_products = Product.objects.filter(
            behaviors__user=user,
            behaviors__action__in=['view', 'purchase', 'add_to_cart']
        ).distinct()
        
        if not user_products.exists():
            return self.trending_products(limit)
        
        # Find products in same categories or with similar tags
        similar_products = Product.objects.filter(
            Q(category__in=[p.category for p in user_products]) |
            Q(brand__in=[p.brand for p in user_products if p.brand])
        ).exclude(
            id__in=[p.id for p in user_products]
        ).filter(
            is_active=True
        ).annotate(
            avg_rating=Avg('ratings__rating')
        ).order_by('-avg_rating', '-created_at')[:limit]
        
        return self._create_recommendations(user, similar_products, 'similar')
    
    def hybrid_recommendations(self, user, limit=10):
        """Combine multiple recommendation strategies"""
        recommendations = []
        
        # Get recommendations from different strategies
        collab_recs = self.collaborative_filtering(user, limit//3)
        content_recs = self.content_based_filtering(user, limit//3)
        similar_recs = self.similar_products_based_on_behavior(user, limit//3)
        
        # Combine and deduplicate
        all_recs = list(collab_recs) + list(content_recs) + list(similar_recs)
        seen_products = set()
        
        for rec in all_recs:
            if hasattr(rec, 'product'):
                product = rec.product
            else:
                product = rec
            
            if product.id not in seen_products:
                recommendations.append(product)
                seen_products.add(product.id)
                
                if len(recommendations) >= limit:
                    break
        
        # Fill remaining slots with trending products
        if len(recommendations) < limit:
            trending = self.trending_products(limit - len(recommendations))
            for product in trending:
                if product.id not in seen_products:
                    recommendations.append(product)
                    if len(recommendations) >= limit:
                        break
        
        return recommendations
    
    def _create_recommendations(self, user, products, rec_type):
        """Create recommendation objects"""
        recommendations = []
        for i, product in enumerate(products):
            # Simple scoring based on position
            score = max(0.1, 1.0 - (i * 0.1))
            
            rec, created = Recommendation.objects.get_or_create(
                user=user,
                product=product,
                recommendation_type=rec_type,
                defaults={
                    'score': score,
                    'reason': f'Recommended based on {rec_type} filtering'
                }
            )
            
            if not created:
                # Update existing recommendation
                rec.score = score
                rec.is_active = True
                rec.save()
            
            recommendations.append(rec)
        
        return recommendations
    
    def update_user_recommendations(self, user):
        """Update all recommendations for a user"""
        # Deactivate old recommendations
        Recommendation.objects.filter(user=user).update(is_active=False)
        
        # Generate new recommendations
        hybrid_recs = self.hybrid_recommendations(user, 10)
        
        return len(hybrid_recs)


# Global instance
recommendation_engine = SimpleRecommendationEngine()
