#!/usr/bin/env python3
"""
Simple script to install packages one by one
Run this script: python install_packages.py
"""

import subprocess
import sys

packages = [
    'Django',
    'djangorestframework', 
    'django-cors-headers',
    'djangorestframework-simplejwt',
    'django-filter',
    'python-decouple',
    'requests'
]

def install_package(package):
    """Install a single package"""
    try:
        print(f"Installing {package}...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        print(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False

def main():
    """Main installation function"""
    print("Starting package installation...")
    
    successful = []
    failed = []
    
    for package in packages:
        if install_package(package):
            successful.append(package)
        else:
            failed.append(package)
    
    print("\n" + "="*50)
    print("INSTALLATION SUMMARY")
    print("="*50)
    
    if successful:
        print(f"✅ Successfully installed ({len(successful)}):")
        for pkg in successful:
            print(f"   - {pkg}")
    
    if failed:
        print(f"\n❌ Failed to install ({len(failed)}):")
        for pkg in failed:
            print(f"   - {pkg}")
        print("\nYou can try installing these manually or skip them for now.")
    
    print(f"\nTotal: {len(successful)}/{len(packages)} packages installed successfully")

if __name__ == "__main__":
    main()
