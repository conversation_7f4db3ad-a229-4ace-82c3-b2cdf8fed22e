<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .nav-link { 
            display: inline-block; 
            margin: 10px; 
            padding: 10px 20px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px;
            cursor: pointer;
        }
        .nav-link.active { background: #28a745; }
        .section { 
            margin: 20px 0; 
            padding: 20px; 
            border: 2px solid #ddd; 
            border-radius: 5px;
            display: none;
        }
        .section.active { display: block; }
        .debug { 
            background: #f8f9fa; 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 5px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Navigation Test</h1>
    
    <div class="debug">
        <strong>Debug Console:</strong> Open browser console (F12) to see navigation logs
    </div>
    
    <!-- Navigation -->
    <nav>
        <a href="#home" class="nav-link active" onclick="testShowSection('home', this)">Home</a>
        <a href="#products" class="nav-link" onclick="testShowSection('products', this)">Products</a>
        <a href="#cart" class="nav-link" onclick="testShowSection('cart', this)">Cart</a>
    </nav>
    
    <!-- Sections -->
    <div id="home-section" class="section active">
        <h2>🏠 Home Section</h2>
        <p>This is the home section. You should see this by default.</p>
        <p>Click on other navigation links to test section switching.</p>
    </div>
    
    <div id="products-section" class="section">
        <h2>📦 Products Section</h2>
        <p>This is the products section. You should see this when clicking "Products".</p>
        <p>Navigation is working if you can see this content!</p>
    </div>
    
    <div id="cart-section" class="section">
        <h2>🛒 Cart Section</h2>
        <p>This is the cart section. You should see this when clicking "Cart".</p>
        <p>Navigation is working if you can see this content!</p>
    </div>
    
    <div class="debug">
        <h3>Test Functions:</h3>
        <button onclick="testShowSection('home')">Test Home</button>
        <button onclick="testShowSection('products')">Test Products</button>
        <button onclick="testShowSection('cart')">Test Cart</button>
        <button onclick="debugInfo()">Debug Info</button>
    </div>
    
    <script>
        let currentSection = 'home';
        
        function testShowSection(sectionName, linkElement) {
            console.log('testShowSection called with:', sectionName);
            
            // Hide all sections
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.classList.remove('active');
                section.style.display = 'none';
            });
            
            // Show target section
            const targetSection = document.getElementById(sectionName + '-section');
            if (targetSection) {
                targetSection.classList.add('active');
                targetSection.style.display = 'block';
                console.log('Showing section:', sectionName);
            } else {
                console.error('Section not found:', sectionName + '-section');
            }
            
            // Update navigation
            if (linkElement) {
                const navLinks = document.querySelectorAll('.nav-link');
                navLinks.forEach(link => link.classList.remove('active'));
                linkElement.classList.add('active');
            }
            
            currentSection = sectionName;
            console.log('Current section set to:', currentSection);
        }
        
        function debugInfo() {
            console.log('=== Debug Info ===');
            console.log('Current section:', currentSection);
            console.log('All sections:', document.querySelectorAll('.section'));
            console.log('Active section:', document.querySelector('.section.active'));
            console.log('Nav links:', document.querySelectorAll('.nav-link'));
            console.log('Active nav link:', document.querySelector('.nav-link.active'));
            console.log('==================');
        }
        
        // Test on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            debugInfo();
        });
    </script>
</body>
</html>
