@echo off
echo ========================================
echo E-commerce Recommendation System
echo Development Server Starter
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python and try again
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv\" (
    echo Error: Virtual environment not found
    echo Please create a virtual environment first:
    echo   python -m venv venv
    pause
    exit /b 1
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Check if Django is installed
python -c "import django" >nul 2>&1
if errorlevel 1 (
    echo Error: Django is not installed
    echo Installing dependencies...
    cd server
    pip install -r requirements.txt
    cd ..
)

REM Start the development server
echo.
echo Starting development servers...
echo Backend: http://127.0.0.1:8000
echo Frontend: http://localhost:3000
echo.
echo Press Ctrl+C to stop both servers
echo.

python start_dev_server.py

pause
