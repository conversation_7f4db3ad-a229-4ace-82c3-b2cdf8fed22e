"""
Payment views for handling Paystack integration
"""
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from django.shortcuts import get_object_or_404
import json
import uuid
from orders.models import Order
from .models import Payment
from .paystack_service import paystack_service


@csrf_exempt
@login_required
@require_http_methods(["POST"])
def initialize_payment(request):
    """Initialize payment with Paystack"""
    try:
        data = json.loads(request.body)
        order_id = data.get('order_id')
        
        if not order_id:
            return JsonResponse({'error': 'Order ID is required'}, status=400)
        
        # Get the order
        order = get_object_or_404(Order, id=order_id, user=request.user)
        
        # Check if order already has a successful payment
        if order.payment_status == 'paid':
            return JsonResponse({'error': 'Order already paid'}, status=400)
        
        # Generate unique reference
        reference = f"order_{order.order_number}_{uuid.uuid4().hex[:8]}"
        
        # Initialize payment with Paystack
        callback_url = request.build_absolute_uri('/api/payments/callback/')
        paystack_response = paystack_service.initialize_payment(
            email=request.user.email,
            amount=float(order.total_amount),
            reference=reference,
            callback_url=callback_url
        )
        
        if not paystack_response.get('status'):
            return JsonResponse({
                'error': 'Failed to initialize payment',
                'message': paystack_response.get('message', 'Unknown error')
            }, status=400)
        
        # Create payment record
        payment = Payment.objects.create(
            user=request.user,
            order=order,
            amount=order.total_amount,
            net_amount=order.total_amount,
            paystack_reference=reference,
            paystack_access_code=paystack_response['data'].get('access_code', ''),
            paystack_response=paystack_response
        )
        
        return JsonResponse({
            'status': True,
            'payment_id': str(payment.id),
            'reference': reference,
            'authorization_url': paystack_response['data'].get('authorization_url'),
            'access_code': paystack_response['data'].get('access_code'),
        })
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["GET", "POST"])
def payment_callback(request):
    """Handle payment callback from Paystack"""
    reference = request.GET.get('reference')
    
    if not reference:
        return JsonResponse({'error': 'Reference is required'}, status=400)
    
    try:
        # Verify payment with Paystack
        verification_response = paystack_service.verify_payment(reference)
        
        if not verification_response.get('status'):
            return JsonResponse({
                'error': 'Payment verification failed',
                'message': verification_response.get('message', 'Unknown error')
            }, status=400)
        
        payment_data = verification_response.get('data', {})
        
        # Update payment record
        try:
            payment = Payment.objects.get(paystack_reference=reference)
            
            if payment_data.get('status') == 'success':
                payment.status = 'success'
                payment.processed_at = timezone.now()
                
                # Update order
                payment.order.payment_status = 'paid'
                payment.order.status = 'confirmed'
                payment.order.save()
                
                # Update transaction fee
                if 'fees' in payment_data:
                    payment.transaction_fee = payment_data['fees'] / 100
                    payment.net_amount = payment.amount - payment.transaction_fee
            else:
                payment.status = 'failed'
                payment.failure_reason = payment_data.get('gateway_response', 'Payment failed')
            
            payment.paystack_response = verification_response
            payment.save()
            
            # Redirect to frontend with status
            frontend_url = f"http://localhost:3000/payment-result?status={payment.status}&reference={reference}"
            return HttpResponse(f"""
                <html>
                    <body>
                        <script>
                            window.location.href = '{frontend_url}';
                        </script>
                        <p>Redirecting...</p>
                    </body>
                </html>
            """)
            
        except Payment.DoesNotExist:
            return JsonResponse({'error': 'Payment not found'}, status=404)
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def paystack_webhook(request):
    """Handle webhook from Paystack"""
    signature = request.META.get('HTTP_X_PAYSTACK_SIGNATURE')
    
    if not signature:
        return HttpResponse('No signature', status=400)
    
    payload = request.body
    result = paystack_service.process_webhook(payload, signature)
    
    if result['status']:
        return HttpResponse('OK')
    else:
        return HttpResponse(result['message'], status=400)


@login_required
def payment_status(request, payment_id):
    """Get payment status"""
    try:
        payment = get_object_or_404(Payment, id=payment_id, user=request.user)
        
        return JsonResponse({
            'id': str(payment.id),
            'status': payment.status,
            'amount': str(payment.amount),
            'currency': payment.currency,
            'reference': payment.paystack_reference,
            'created_at': payment.created_at.isoformat(),
            'processed_at': payment.processed_at.isoformat() if payment.processed_at else None,
            'order': {
                'id': str(payment.order.id),
                'order_number': payment.order.order_number,
                'status': payment.order.status,
            }
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
def user_payments(request):
    """Get user's payment history"""
    payments = Payment.objects.filter(user=request.user).order_by('-created_at')
    
    payments_data = []
    for payment in payments:
        payments_data.append({
            'id': str(payment.id),
            'status': payment.status,
            'amount': str(payment.amount),
            'currency': payment.currency,
            'reference': payment.paystack_reference,
            'created_at': payment.created_at.isoformat(),
            'order': {
                'id': str(payment.order.id),
                'order_number': payment.order.order_number,
            }
        })
    
    return JsonResponse({'payments': payments_data})
