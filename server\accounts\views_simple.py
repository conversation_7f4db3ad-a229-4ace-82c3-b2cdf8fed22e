"""
Simple Django views for authentication without DRF dependencies
"""
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt, ensure_csrf_cookie
from django.views.decorators.http import require_http_methods
from django.contrib.auth import authenticate, login, logout, get_user_model
from django.contrib.auth.decorators import login_required
from django.contrib.sessions.models import Session
from django.middleware.csrf import get_token
from functools import wraps
import json
import uuid
import hashlib
from datetime import datetime, timedelta
from .models import UserProfile, UserBehavior

User = get_user_model()

# Simple in-memory token storage (use Redis or database in production)
active_tokens = {}

def generate_auth_token(user):
    """Generate a simple authentication token"""
    token = str(uuid.uuid4())
    active_tokens[token] = {
        'user_id': user.id,
        'created_at': datetime.now(),
        'expires_at': datetime.now() + timedelta(hours=24)
    }
    return token

def get_user_from_token(token):
    """Get user from authentication token"""
    if token in active_tokens:
        token_data = active_tokens[token]
        if datetime.now() < token_data['expires_at']:
            try:
                return User.objects.get(id=token_data['user_id'])
            except User.DoesNotExist:
                pass
        else:
            # Token expired, remove it
            del active_tokens[token]
    return None

def ajax_login_required(view_func):
    """
    Decorator for views that checks that the user is logged in.
    Uses custom token authentication as fallback for session issues.
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        # First try Django session authentication
        if request.user.is_authenticated:
            return view_func(request, *args, **kwargs)

        # Fallback to custom token authentication
        auth_token = request.headers.get('X-Auth-Token') or request.COOKIES.get('auth_token')
        if auth_token:
            user = get_user_from_token(auth_token)
            if user:
                request.user = user
                return view_func(request, *args, **kwargs)

        # No valid authentication found
        return JsonResponse({'error': 'Authentication required'}, status=401)

    return _wrapped_view


@csrf_exempt
@require_http_methods(["POST"])
def user_register(request):
    """User registration"""
    try:
        data = json.loads(request.body)
        
        # Validate required fields
        required_fields = ['email', 'username', 'password', 'first_name', 'last_name']
        for field in required_fields:
            if not data.get(field):
                return JsonResponse({'error': f'{field} is required'}, status=400)
        
        # Check if user exists
        if User.objects.filter(email=data['email']).exists():
            return JsonResponse({'error': 'User with this email already exists'}, status=400)
        
        if User.objects.filter(username=data['username']).exists():
            return JsonResponse({'error': 'User with this username already exists'}, status=400)
        
        # Create user
        user = User.objects.create_user(
            email=data['email'],
            username=data['username'],
            password=data['password'],
            first_name=data['first_name'],
            last_name=data['last_name'],
            phone_number=data.get('phone_number', '')
        )
        
        # Login user
        login(request, user)

        # Get CSRF token for subsequent requests
        csrf_token = get_token(request)
        response = JsonResponse({
            'message': 'User registered successfully',
            'user': {
                'id': user.id,
                'email': user.email,
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
            },
            'csrfToken': csrf_token
        }, status=201)
        # Set the CSRF token cookie
        response.set_cookie('csrftoken', csrf_token, max_age=********, samesite='Lax')
        return response
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def user_login(request):
    """User login"""
    try:
        data = json.loads(request.body)
        
        email = data.get('email')
        password = data.get('password')
        
        if not email or not password:
            return JsonResponse({'error': 'Email and password are required'}, status=400)
        
        # Authenticate user
        user = authenticate(request, username=email, password=password)
        
        if user is not None:
            if user.is_active:
                login(request, user)
                print(f"DEBUG: User logged in: {user}")

                # Generate custom auth token as backup
                auth_token = generate_auth_token(user)
                print(f"DEBUG: Generated auth token: {auth_token}")

                # Get CSRF token for subsequent requests
                csrf_token = get_token(request)
                response = JsonResponse({
                    'message': 'Login successful',
                    'user': {
                        'id': user.id,
                        'email': user.email,
                        'username': user.username,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                    },
                    'csrfToken': csrf_token,
                    'authToken': auth_token
                })

                # Set cookies
                response.set_cookie('csrftoken', csrf_token, max_age=********, samesite=None, secure=False)
                response.set_cookie('auth_token', auth_token, max_age=86400, samesite=None, secure=False)

                print(f"DEBUG: Login response created with tokens")
                return response
            else:
                return JsonResponse({'error': 'User account is disabled'}, status=400)
        else:
            return JsonResponse({'error': 'Invalid email or password'}, status=400)
            
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def user_logout(request):
    """User logout"""
    logout(request)
    return JsonResponse({'message': 'Logout successful'})


@ajax_login_required
def user_profile(request):
    """Get user profile"""
    user = request.user
    
    # Get or create profile
    profile, created = UserProfile.objects.get_or_create(user=user)
    
    data = {
        'id': user.id,
        'email': user.email,
        'username': user.username,
        'first_name': user.first_name,
        'last_name': user.last_name,
        'phone_number': user.phone_number,
        'date_of_birth': user.date_of_birth.isoformat() if user.date_of_birth else None,
        'is_verified': user.is_verified,
        'date_joined': user.date_joined.isoformat(),
        'profile': {
            'gender': profile.gender,
            'address': profile.address,
            'city': profile.city,
            'state': profile.state,
            'country': profile.country,
            'postal_code': profile.postal_code,
            'preferences': profile.preferences,
        }
    }
    
    return JsonResponse(data)


@csrf_exempt
@ajax_login_required
@require_http_methods(["POST"])
def track_behavior(request):
    """Track user behavior"""
    try:
        data = json.loads(request.body)
        
        action = data.get('action')
        product_id = data.get('product_id')
        search_query = data.get('search_query', '')
        metadata = data.get('metadata', {})
        
        if not action:
            return JsonResponse({'error': 'Action is required'}, status=400)
        
        # Get client IP and user agent
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip_address = x_forwarded_for.split(',')[0]
        else:
            ip_address = request.META.get('REMOTE_ADDR')
        
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # Create behavior record
        behavior = UserBehavior.objects.create(
            user=request.user,
            action=action,
            product_id=product_id,
            search_query=search_query,
            ip_address=ip_address,
            user_agent=user_agent,
            metadata=metadata
        )
        
        return JsonResponse({
            'message': 'Behavior tracked successfully',
            'id': behavior.id
        })
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@ensure_csrf_cookie
def check_auth(request):
    """Check if user is authenticated"""
    if request.user.is_authenticated:
        return JsonResponse({
            'authenticated': True,
            'user': {
                'id': request.user.id,
                'email': request.user.email,
                'username': request.user.username,
                'first_name': request.user.first_name,
                'last_name': request.user.last_name,
            }
        })
    else:
        return JsonResponse({'authenticated': False})


@ensure_csrf_cookie
def get_csrf_token(request):
    """Get CSRF token"""
    token = get_token(request)
    response = JsonResponse({'csrfToken': token})
    # Also set the cookie explicitly
    response.set_cookie('csrftoken', token, max_age=********, samesite='Lax')
    return response
