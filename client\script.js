// API Configuration
const API_BASE_URL = 'http://127.0.0.1:8000/api';

// Global variables
let products = [];
let categories = [];
let currentUser = null;
let currentSection = 'home';

// API Functions
async function fetchProducts() {
    try {
        const response = await fetch(`${API_BASE_URL}/products/`);
        const data = await response.json();
        products = data.results || [];
        return products;
    } catch (error) {
        console.error('Error fetching products:', error);
        return [];
    }
}

async function fetchCategories() {
    try {
        const response = await fetch(`${API_BASE_URL}/products/categories/`);
        const data = await response.json();
        categories = data.results || [];
        return categories;
    } catch (error) {
        console.error('Error fetching categories:', error);
        return [];
    }
}

async function fetchFeaturedProducts() {
    try {
        const response = await fetch(`${API_BASE_URL}/products/featured/`);
        const data = await response.json();
        return data.results || [];
    } catch (error) {
        console.error('Error fetching featured products:', error);
        return [];
    }
}

async function fetchTrendingProducts() {
    try {
        const response = await fetch(`${API_BASE_URL}/recommendations/trending/`);
        const data = await response.json();
        return data.products || [];
    } catch (error) {
        console.error('Error fetching trending products:', error);
        return [];
    }
}
async function trackUserBehavior(action, productId = null, metadata = {}) {
    if (!currentUser) return;
    try {
        await fetch(`${API_BASE_URL}/auth/behavior/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            credentials: 'include',
            body: JSON.stringify({
                action: action,
                product_id: productId,
                metadata: metadata
            })
        });
    } catch (error) {
        console.error('Error tracking behavior:', error);
    }
}
// Cart functionality
let cart = JSON.parse(localStorage.getItem('cart')) || [];

// DOM elements
const navLinks = document.querySelectorAll('.nav-link');
const sections = document.querySelectorAll('.section');
const hamburger = document.getElementById('hamburger');
const navMenu = document.getElementById('nav-menu');
const searchInput = document.getElementById('search-input');
const categoryFilter = document.getElementById('category-filter');
const sortFilter = document.getElementById('sort-filter');
const modal = document.getElementById('product-modal');
// Removed duplicate closeModal variable declaration. Use the closeModal function instead.

// Initialize CSRF token
async function initializeCSRF() {
    try {
        const response = await fetch(`${API_BASE_URL}/auth/csrf/`, {
            credentials: 'include'
        });
        if (response.ok) {
            const data = await response.json();
            console.log('CSRF token initialized successfully:', data);
            // Store the CSRF token if provided
            if (data.csrfToken) {
                // Set it as a cookie manually if needed
                document.cookie = `csrftoken=${data.csrfToken}; path=/`;
            }
        } else {
            console.error('Failed to initialize CSRF token:', response.status);
        }
    } catch (error) {
        console.error('Error initializing CSRF:', error);
    }
}

// Initialize app
document.addEventListener('DOMContentLoaded', async function() {
    console.log('DOM Content Loaded - Initializing app...');

    // Initialize CSRF token first
    await initializeCSRF();

    updateCartCount();
    setupEventListeners();

    // Check authentication status
    await checkAuthStatus();

    // Load data from API
    await loadInitialData();

    // Set up navigation
    setupNavigation();

    // Add fallback navigation handlers directly to HTML elements
    addFallbackNavigation();

    console.log('App initialization complete');
});

// Backup initialization in case DOMContentLoaded doesn't fire
window.addEventListener('load', function() {
    console.log('Window loaded - backup initialization');

    // Check if navigation was already set up
    const navLinks = document.querySelectorAll('.nav-link');
    if (navLinks.length > 0) {
        console.log('Found nav links, setting up navigation as backup');
        setupNavigation();
        addFallbackNavigation();
    }
});

// Fallback navigation function
function addFallbackNavigation() {
    console.log('Adding fallback navigation...');

    // Add onclick handlers directly to navigation links
    const homeLink = document.querySelector('a[href="#home"]');
    const productsLink = document.querySelector('a[href="#products"]');
    const cartLink = document.querySelector('a[href="#cart"]');

    if (homeLink) {
        homeLink.onclick = function(e) {
            e.preventDefault();
            console.log('Home link clicked (fallback)');
            showSection('home');
            updateActiveNav(this);
        };
    }

    if (productsLink) {
        productsLink.onclick = function(e) {
            e.preventDefault();
            console.log('Products link clicked (fallback)');
            showSection('products');
            updateActiveNav(this);
        };
    }

    if (cartLink) {
        cartLink.onclick = function(e) {
            e.preventDefault();
            console.log('Cart link clicked (fallback)');
            showSection('cart');
            updateActiveNav(this);
        };
    }
}

function updateActiveNav(activeLink) {
    // Remove active class from all nav links
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => link.classList.remove('active'));

    // Add active class to clicked link
    if (activeLink && !activeLink.id || activeLink.id !== 'auth-link') {
        activeLink.classList.add('active');
    }
}

async function loadInitialData() {
    try {
        // Show loading state
        showLoading();

        // Load categories and products
        await Promise.all([
            fetchCategories(),
            fetchProducts()
        ]);

        // Update category filter
        updateCategoryFilter();

        // Display products
        await displayFeaturedProducts();
        await displayAllProducts();
        await displayTrendingProducts();

        // Load recommendations if user is logged in
        if (currentUser) {
            await displayRecommendations();
        }

        hideLoading();
    } catch (error) {
        console.error('Error loading initial data:', error);
        hideLoading();
        showError('Failed to load data. Please refresh the page.');
    }
}

function showLoading() {
    const featuredContainer = document.getElementById('featured-products');
    const allProductsContainer = document.getElementById('all-products');

    featuredContainer.innerHTML = '<div class="loading">Loading featured products...</div>';
    allProductsContainer.innerHTML = '<div class="loading">Loading products...</div>';
}

function hideLoading() {
    // Loading will be replaced by actual content
}

function showError(message) {
    const featuredContainer = document.getElementById('featured-products');
    const allProductsContainer = document.getElementById('all-products');

    const errorHtml = `<div class="error">${message}</div>`;
    featuredContainer.innerHTML = errorHtml;
    allProductsContainer.innerHTML = errorHtml;
}

function updateCategoryFilter() {
    const categoryFilter = document.getElementById('category-filter');

    // Clear existing options except "All Categories"
    categoryFilter.innerHTML = '<option value="all">All Categories</option>';

    // Add categories from API
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.slug;
        option.textContent = category.name;
        categoryFilter.appendChild(option);
    });
}

// Event listeners
function setupEventListeners() {
    // Navigation
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('href').substring(1);
            showSection(section);
        });
    });

    // Mobile menu
    hamburger.addEventListener('click', () => {
        navMenu.classList.toggle('active');
    });

    // Search
    searchInput.addEventListener('input', filterProducts);
    
    // Filters
    categoryFilter.addEventListener('change', filterProducts);
    sortFilter.addEventListener('change', filterProducts);

    // Modal
    document.querySelectorAll('.close').forEach(function(closeBtn) {
        closeBtn.addEventListener('click', function() {
            modal.style.display = 'none';
        });
    });

    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
}

// Navigation functions
function showSection(sectionName) {
    // Update navigation
    navLinks.forEach(link => link.classList.remove('active'));
    sections.forEach(section => section.classList.remove('active'));
    
    document.getElementById(sectionName).classList.add('active');
    document.querySelector(`[href="#${sectionName}"]`).classList.add('active');
    
    currentSection = sectionName;
    navMenu.classList.remove('active');
    
    // Load section-specific content
    if (sectionName === 'cart') {
        displayCart();
    }
}

// Product display functions
async function displayFeaturedProducts() {
    const featuredContainer = document.getElementById('featured-products');

    try {
        // Try to get featured products from API, fallback to first 6 products
        let featuredProducts = await fetchFeaturedProducts();
        if (featuredProducts.length === 0) {
            featuredProducts = products.slice(0, 6);
        }

        featuredContainer.innerHTML = featuredProducts.map(product => createProductCard(product)).join('');
    } catch (error) {
        console.error('Error displaying featured products:', error);
        featuredContainer.innerHTML = '<div class="error">Failed to load featured products</div>';
    }
}

async function displayAllProducts() {
    const productsContainer = document.getElementById('all-products');

    try {
        if (products.length === 0) {
            await fetchProducts();
        }

        productsContainer.innerHTML = products.map(product => createProductCard(product)).join('');
    } catch (error) {
        console.error('Error displaying products:', error);
        productsContainer.innerHTML = '<div class="error">Failed to load products</div>';
    }
}

async function displayTrendingProducts() {
    const trendingContainer = document.getElementById('trending-products');

    try {
        const trendingProducts = await fetchTrendingProducts();

        if (trendingProducts.length === 0) {
            trendingContainer.innerHTML = '<div class="no-results">No trending products available</div>';
        } else {
            trendingContainer.innerHTML = trendingProducts.map(product => createProductCard(product)).join('');
        }
    } catch (error) {
        console.error('Error displaying trending products:', error);
        trendingContainer.innerHTML = '<div class="error">Failed to load trending products</div>';
    }
}

async function displayRecommendations() {
    const recommendationsContainer = document.getElementById('recommended-products');
    const recommendationsSection = document.getElementById('recommendations-section');
    if (!currentUser) {
        recommendationsSection.style.display = 'none';
        return;
    }
    try {
        const response = await fetch(`${API_BASE_URL}/recommendations/personalized/`, {
            credentials: 'include'
        });
        if (response.ok) {
            const data = await response.json();
            const recommendedProducts = data.products || [];
            if (recommendedProducts.length === 0) {
                recommendationsSection.style.display = 'none';
            } else {
                recommendationsSection.style.display = 'block';
                recommendationsContainer.innerHTML = recommendedProducts.map(product => createProductCard(product)).join('');
            }
        } else {
            recommendationsSection.style.display = 'none';
        }
    } catch (error) {
        console.error('Error displaying recommendations:', error);
        recommendationsSection.style.display = 'none';
    }
}

function createProductCard(product) {
    // Handle both API format and legacy format
    const rating = product.average_rating || product.rating || 0;
    const stars = '★'.repeat(Math.floor(rating)) + '☆'.repeat(5 - Math.floor(rating));
    const price = product.price || 0;
    const category = product.category || product.category_name || '';
    const description = product.description || product.short_description || '';

    // Track product view
    trackUserBehavior('view', product.id);

    return `
        <div class="product-card" onclick="showProductDetail(${product.id})">
            <img src="${product.image}" alt="${product.name}" class="product-image" onerror="this.src='https://via.placeholder.com/400x400?text=No+Image'">
            <div class="product-info">
                <div class="product-rating">
                    <span class="stars">${stars}</span>
                    <span>(${rating.toFixed(1)})</span>
                </div>
                <h3 class="product-name">${product.name}</h3>
                <p class="product-description">${description}</p>
                <div class="product-footer">
                    <span class="product-price">$${parseFloat(price).toFixed(2)}</span>
                    <button class="add-to-cart" onclick="event.stopPropagation(); addToCart(${product.id})">
                        🛒 Add
                    </button>
                </div>
            </div>
        </div>
    `;
}

// Product detail modal
async function showProductDetail(productId) {
    let product = products.find(p => p.id === productId);

    if (!product) {
        // Try to fetch product details from API
        try {
            const response = await fetch(`${API_BASE_URL}/products/${productId}/`);
            if (response.ok) {
                product = await response.json();
            }
        } catch (error) {
            console.error('Error fetching product details:', error);
        }
    }

    if (!product) {
        showNotification('Product not found');
        return;
    }

    // Track product click
    trackUserBehavior('click', product.id);

    const rating = product.average_rating || product.rating || 0;
    const stars = '★'.repeat(Math.floor(rating)) + '☆'.repeat(5 - Math.floor(rating));
    const category = product.category || product.category_name || '';
    const price = parseFloat(product.price || 0);

    document.getElementById('product-detail').innerHTML = `
        <div>
            <img src="${product.image}" alt="${product.name}" class="product-detail-image" onerror="this.src='https://via.placeholder.com/400x400?text=No+Image'">
        </div>
        <div class="product-detail-info">
            <h2>${product.name}</h2>
            <div class="product-rating">
                <span class="stars">${stars}</span>
                <span>(${rating.toFixed(1)}) • ${category}</span>
            </div>
            <div class="product-detail-price">$${price.toFixed(2)}</div>
            <p>${product.description}</p>

            <div class="quantity-selector">
                <span>Quantity:</span>
                <button class="quantity-btn" onclick="changeQuantity(-1)">-</button>
                <input type="number" id="quantity" class="quantity-input" value="1" min="1">
                <button class="quantity-btn" onclick="changeQuantity(1)">+</button>
            </div>

            <button class="cta-btn" onclick="addToCartWithQuantity(${product.id})">
                🛒 Add to Cart
            </button>
        </div>
    `;

    modal.style.display = 'block';
}

function changeQuantity(change) {
    const quantityInput = document.getElementById('quantity');
    let quantity = parseInt(quantityInput.value) + change;
    if (quantity < 1) quantity = 1;
    quantityInput.value = quantity;
}

// Cart functions
async function addToCart(productId) {
    if (!currentUser) {
        showNotification('Please login to add items to cart', 'warning');
        showLoginModal();
        return;
    }

    console.log('Adding to cart - User:', currentUser);
    console.log('CSRF Token:', getCookie('csrftoken'));

    try {
        const response = await fetch(`${API_BASE_URL}/orders/cart/add/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            credentials: 'include',
            body: JSON.stringify({
                product_id: productId,
                quantity: 1
            })
        });

        console.log('Add to cart response status:', response.status);

        const data = await response.json();
        console.log('Add to cart response data:', data);

        if (response.ok) {
            showNotification(data.message);
            await updateCartCount();
            trackUserBehavior('add_to_cart', productId);
        } else {
            showNotification(data.error || 'Failed to add to cart', 'error');
        }
    } catch (error) {
        console.error('Error adding to cart:', error);
        showNotification('Failed to add to cart', 'error');
    }
}

async function addToCartWithQuantity(productId) {
    const quantity = parseInt(document.getElementById('quantity').value) || 1;

    if (!currentUser) {
        showNotification('Please login to add items to cart', 'warning');
        showLoginModal();
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/orders/cart/add/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            credentials: 'include',
            body: JSON.stringify({
                product_id: productId,
                quantity: quantity
            })
        });

        const data = await response.json();

        if (response.ok) {
            showNotification(data.message);
            await updateCartCount();
            closeModal('product-modal');

            // Track add to cart behavior
            trackUserBehavior('add_to_cart', productId, { quantity: quantity });
        } else {
            showNotification(data.error || 'Failed to add to cart', 'error');
        }
    } catch (error) {
        console.error('Error adding to cart:', error);
        showNotification('Failed to add to cart', 'error');
    }
}

// Duplicate function removed - using async version above

async function removeFromCart(itemId) {
    try {
        const response = await fetch(`${API_BASE_URL}/orders/cart/remove/${itemId}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            },
            credentials: 'include'
        });

        const data = await response.json();

        if (response.ok) {
            showNotification(data.message);
            await updateCartCount();
            await displayCart();
        } else {
            showNotification(data.error || 'Failed to remove item', 'error');
        }
    } catch (error) {
        console.error('Error removing from cart:', error);
        showNotification('Failed to remove item', 'error');
    }
}

async function updateCartItemQuantity(itemId, newQuantity) {
    if (newQuantity <= 0) {
        await removeFromCart(itemId);
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/orders/cart/update/${itemId}/`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            credentials: 'include',
            body: JSON.stringify({
                quantity: newQuantity
            })
        });

        const data = await response.json();

        if (response.ok) {
            await updateCartCount();
            await displayCart();
        } else {
            showNotification(data.error || 'Failed to update quantity', 'error');
        }
    } catch (error) {
        console.error('Error updating cart item:', error);
        showNotification('Failed to update quantity', 'error');
    }
}

async function clearCart() {
    try {
        const response = await fetch(`${API_BASE_URL}/orders/cart/clear/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            },
            credentials: 'include'
        });

        const data = await response.json();

        if (response.ok) {
            showNotification(data.message);
            await updateCartCount();
            await displayCart();
        } else {
            showNotification(data.error || 'Failed to clear cart', 'error');
        }
    } catch (error) {
        console.error('Error clearing cart:', error);
        showNotification('Failed to clear cart', 'error');
    }
}

// Cart display function
function showCart() {
    // Use the section switching system
    showSection('cart');
}

// Checkout and Payment Functions
async function showCheckout() {
    if (!currentUser) {
        showNotification('Please login to checkout', 'warning');
        showLoginModal();
        return;
    }

    const checkoutModal = document.getElementById('checkout-modal');
    const checkoutContent = document.getElementById('checkout-content');

    checkoutContent.innerHTML = `
        <form id="checkout-form" onsubmit="processCheckout(event)">
            <h3>Shipping Address</h3>
            <div class="form-group">
                <label for="address">Street Address:</label>
                <input type="text" id="address" required>
            </div>
            <div class="form-group">
                <label for="city">City:</label>
                <input type="text" id="city" required>
            </div>
            <div class="form-group">
                <label for="state">State:</label>
                <input type="text" id="state" required>
            </div>
            <div class="form-group">
                <label for="postal_code">Postal Code:</label>
                <input type="text" id="postal_code" required>
            </div>
            <div class="form-group">
                <label for="country">Country:</label>
                <input type="text" id="country" value="Nigeria" required>
            </div>

            <h3>Payment Method</h3>
            <div class="payment-methods">
                <label>
                    <input type="radio" name="payment_method" value="paystack" checked>
                    💳 Pay with Card (Paystack)
                </label>
            </div>

            <button type="submit" class="cta-btn">Place Order & Pay</button>
        </form>
    `;

    checkoutModal.style.display = 'block';
}

async function processCheckout(event) {
    event.preventDefault();

    const shippingAddress = {
        address: document.getElementById('address').value,
        city: document.getElementById('city').value,
        state: document.getElementById('state').value,
        postal_code: document.getElementById('postal_code').value,
        country: document.getElementById('country').value,
    };

    try {
        // Create order
        const orderResponse = await fetch(`${API_BASE_URL}/orders/create/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            credentials: 'include',
            body: JSON.stringify({
                shipping_address: shippingAddress
            })
        });

        const orderData = await orderResponse.json();

        if (!orderResponse.ok) {
            showNotification(orderData.error || 'Failed to create order', 'error');
            return;
        }

        // Initialize payment
        const paymentResponse = await fetch(`${API_BASE_URL}/payments/initialize/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            credentials: 'include',
            body: JSON.stringify({
                order_id: orderData.order_id
            })
        });

        const paymentData = await paymentResponse.json();

        if (!paymentResponse.ok) {
            showNotification(paymentData.error || 'Failed to initialize payment', 'error');
            return;
        }

        // Redirect to Paystack
        if (paymentData.authorization_url) {
            window.location.href = paymentData.authorization_url;
        } else {
            showNotification('Payment initialization failed', 'error');
        }

    } catch (error) {
        console.error('Checkout error:', error);
        showNotification('Checkout failed. Please try again.', 'error');
    }
}

async function updateCartCount() {
    if (!currentUser) {
        document.getElementById('cart-count').textContent = '0';
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/orders/cart/`, {
            credentials: 'include'
        });

        if (response.ok) {
            const data = await response.json();
            document.getElementById('cart-count').textContent = data.total_items || '0';
        } else {
            document.getElementById('cart-count').textContent = '0';
        }
    } catch (error) {
        console.error('Error updating cart count:', error);
        document.getElementById('cart-count').textContent = '0';
    }
}

function saveCart() {
    // Cart is now saved on the server, this function is kept for compatibility
}

async function displayCart() {
    if (!currentUser) {
        showNotification('Please login to view cart', 'warning');
        showLoginModal();
        return;
    }

    const cartItemsContainer = document.getElementById('cart-items');
    const cartSummaryContainer = document.getElementById('cart-summary');

    try {
        const response = await fetch(`${API_BASE_URL}/orders/cart/`, {
            credentials: 'include'
        });

        if (response.ok) {
            const data = await response.json();

            if (data.items.length === 0) {
                cartItemsContainer.innerHTML = `
                    <div class="empty-cart">
                        <h3>Your cart is empty</h3>
                        <p>Start shopping to add items to your cart</p>
                        <button class="cta-btn" onclick="showSection('products')">Shop Now</button>
                    </div>
                `;
                cartSummaryContainer.innerHTML = '';
                return;
            }

            // Display cart items from API
            cartItemsContainer.innerHTML = data.items.map(item => `
                <div class="cart-item">
                    <img src="${item.product.image}" alt="${item.product.name}" class="cart-item-image" onerror="this.src='https://via.placeholder.com/100x100?text=No+Image'">
                    <div class="cart-item-info">
                        <h4>${item.product.name}</h4>
                        <p>$${parseFloat(item.unit_price).toFixed(2)} each</p>
                        <div class="quantity-controls">
                            <button onclick="updateCartItemQuantity(${item.id}, ${item.quantity - 1})" ${item.quantity <= 1 ? 'disabled' : ''}>-</button>
                            <span>${item.quantity}</span>
                            <button onclick="updateCartItemQuantity(${item.id}, ${item.quantity + 1})">+</button>
                        </div>
                    </div>
                    <div class="cart-item-controls">
                        <p class="item-total">$${parseFloat(item.total_price).toFixed(2)}</p>
                        <button onclick="removeFromCart(${item.id})" class="remove-btn">Remove</button>
                    </div>
                </div>
            `).join('');

            cartSummaryContainer.innerHTML = `
                <div class="cart-total">
                    <h3>Total: $${parseFloat(data.total_price).toFixed(2)}</h3>
                    <button class="cta-btn" onclick="showCheckout()">Proceed to Checkout</button>
                    <button class="secondary-btn" onclick="clearCart()">Clear Cart</button>
                </div>
            `;
        } else {
            cartItemsContainer.innerHTML = '<p>Error loading cart</p>';
            cartSummaryContainer.innerHTML = '';
        }
    } catch (error) {
        console.error('Error loading cart:', error);
        cartItemsContainer.innerHTML = '<p>Error loading cart</p>';
        cartSummaryContainer.innerHTML = '';
    }
}

// Old cart functions removed - using API-based functions instead

function checkout() {
    // Redirect to new checkout system
    showCheckout();
}

// Filter and search functions
async function filterProducts() {
    const searchInput = document.getElementById('search-input');
    const categoryFilter = document.getElementById('category-filter');
    const sortFilter = document.getElementById('sort-filter');

    const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
    const category = categoryFilter ? categoryFilter.value : 'all';
    const sortBy = sortFilter ? sortFilter.value : 'name';

    // Track search and filter behavior
    if (searchTerm) {
        trackUserBehavior('search', null, { search_query: searchTerm });
    }
    if (category !== 'all') {
        trackUserBehavior('filter', null, { category: category });
    }

    try {
        let filteredProducts;

        // Build API query parameters
        const params = new URLSearchParams();
        if (searchTerm) params.append('search', searchTerm);
        if (category !== 'all') params.append('category', category);

        // Try API search/filter first
        if (searchTerm || category !== 'all') {
            const response = await fetch(`${API_BASE_URL}/products/?${params.toString()}`);
            if (response.ok) {
                const data = await response.json();
                filteredProducts = data.results || [];
            } else {
                throw new Error('API request failed');
            }
        } else {
            // No filters, show all products
            if (products.length === 0) {
                await fetchProducts();
            }
            filteredProducts = [...products];
        }

        // Sort products locally
        filteredProducts.sort((a, b) => {
            const priceA = parseFloat(a.price || 0);
            const priceB = parseFloat(b.price || 0);
            const ratingA = a.average_rating || a.rating || 0;
            const ratingB = b.average_rating || b.rating || 0;

            switch (sortBy) {
                case 'price-low':
                    return priceA - priceB;
                case 'price-high':
                    return priceB - priceA;
                case 'rating':
                    return ratingB - ratingA;
                default:
                    return a.name.localeCompare(b.name);
            }
        });

        const productsContainer = document.getElementById('all-products');
        if (filteredProducts.length === 0) {
            productsContainer.innerHTML = `
                <div style="grid-column: 1/-1; text-align: center; padding: 4rem;">
                    <h3>No products found</h3>
                    <p>Try adjusting your search or filter criteria.</p>
                </div>
            `;
        } else {
            productsContainer.innerHTML = filteredProducts.map(product => createProductCard(product)).join('');
        }
    } catch (error) {
        console.error('Error filtering products:', error);

        // Fallback to local filtering
        let filteredProducts = products.filter(product => {
            const productCategory = product.category || product.category_name || '';
            const matchesSearch = !searchTerm ||
                product.name.toLowerCase().includes(searchTerm) ||
                productCategory.toLowerCase().includes(searchTerm);
            const matchesCategory = category === 'all' || productCategory === category;
            return matchesSearch && matchesCategory;
        });

        const productsContainer = document.getElementById('all-products');
        if (filteredProducts.length === 0) {
            productsContainer.innerHTML = `
                <div style="grid-column: 1/-1; text-align: center; padding: 4rem;">
                    <h3>No products found</h3>
                    <p>Try adjusting your search or filter criteria.</p>
                </div>
            `;
        } else {
            productsContainer.innerHTML = filteredProducts.map(product => createProductCard(product)).join('');
        }
    }
}

// Legacy function names for compatibility
function searchProducts() {
    filterProducts();
}

function filterByCategory() {
    filterProducts();
}

// Authentication Functions
async function handleLogin(event) {
    event.preventDefault();

    const email = document.getElementById('login-email').value;
    const password = document.getElementById('login-password').value;

    try {
        const response = await fetch(`${API_BASE_URL}/auth/login/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            credentials: 'include',
            body: JSON.stringify({
                email: email,
                password: password
            })
        });

        const data = await response.json();
        console.log('Login response status:', response.status);
        console.log('Login response data:', data);

        if (response.ok) {
            currentUser = data.user;
            console.log('User logged in:', currentUser);

            // Handle CSRF token from login response
            if (data.csrfToken) {
                console.log('CSRF token received from login:', data.csrfToken);
                document.cookie = `csrftoken=${data.csrfToken}; path=/; SameSite=Lax`;
            }

            updateAuthUI();
            closeModal('login-modal');
            showNotification(`Welcome back, ${currentUser.first_name}!`);

            // Load recommendations for logged in user
            await displayRecommendations();

            // Update cart count
            await updateCartCount();
        } else {
            showNotification(data.error || 'Login failed', 'error');
        }
    } catch (error) {
        console.error('Login error:', error);
        showNotification('Login failed. Please try again.', 'error');
    }
}

async function handleRegister(event) {
    event.preventDefault();

    const formData = {
        first_name: document.getElementById('register-first-name').value,
        last_name: document.getElementById('register-last-name').value,
        username: document.getElementById('register-username').value,
        email: document.getElementById('register-email').value,
        password: document.getElementById('register-password').value
    };

    try {
        const response = await fetch(`${API_BASE_URL}/auth/register/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            credentials: 'include',
            body: JSON.stringify(formData)
        });

        const data = await response.json();

        if (response.ok) {
            currentUser = data.user;

            // Handle CSRF token from registration response
            if (data.csrfToken) {
                console.log('CSRF token received from registration:', data.csrfToken);
                document.cookie = `csrftoken=${data.csrfToken}; path=/; SameSite=Lax`;
            }

            updateAuthUI();
            closeModal('register-modal');
            showNotification(`Welcome to ShopWave, ${currentUser.first_name}!`);

            // Load recommendations for new user
            await displayRecommendations();

            // Update cart count
            await updateCartCount();
        } else {
            showNotification(data.error || 'Registration failed', 'error');
        }
    } catch (error) {
        console.error('Registration error:', error);
        showNotification('Registration failed. Please try again.', 'error');
    }
}

async function handleLogout() {
    try {
        await fetch(`${API_BASE_URL}/auth/logout/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            },
            credentials: 'include'
        });

        currentUser = null;
        updateAuthUI();

        // Hide recommendations section
        document.getElementById('recommendations-section').style.display = 'none';

        // Reset cart count
        await updateCartCount();

        showNotification('Logged out successfully');
    } catch (error) {
        console.error('Logout error:', error);
        currentUser = null;
        updateAuthUI();
        showNotification('Logged out');
    }
}

function handleAuthClick() {
    if (currentUser) {
        handleLogout();
    } else {
        showLoginModal();
    }
}

function showLoginModal() {
    document.getElementById('login-modal').style.display = 'block';
}

function showRegisterModal() {
    closeModal('login-modal');
    document.getElementById('register-modal').style.display = 'block';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function updateAuthUI() {
    const authLink = document.getElementById('auth-link');
    if (currentUser) {
        authLink.textContent = `Logout (${currentUser.first_name})`;
    } else {
        authLink.textContent = 'Login';
    }
}

async function checkAuthStatus() {
    try {
        const response = await fetch(`${API_BASE_URL}/auth/check/`, {
            credentials: 'include'
        });

        if (response.ok) {
            const data = await response.json();
            if (data.authenticated) {
                currentUser = data.user;
                updateAuthUI();
            }
        }
    } catch (error) {
        console.error('Auth check error:', error);
    }
}

// Notification function
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');

    const colors = {
        success: 'linear-gradient(135deg, #10b981, #059669)',
        error: 'linear-gradient(135deg, #ef4444, #dc2626)',
        info: 'linear-gradient(135deg, #3b82f6, #2563eb)',
        warning: 'linear-gradient(135deg, #f59e0b, #d97706)'
    };

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${colors[type] || colors.success};
        color: white;
        padding: 1rem 2rem;
        border-radius: 50px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        z-index: 3000;
        animation: slideIn 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;
    notification.textContent = message;

    // Add animation styles if not already added
    if (!document.getElementById('notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideIn 0.3s ease reverse';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Navigation Functions
function setupNavigation() {
    console.log('Setting up navigation...'); // Debug log

    // Add click handlers to navigation links
    const navLinks = document.querySelectorAll('.nav-link');
    console.log('Found nav links:', navLinks.length); // Debug log

    navLinks.forEach((link, index) => {
        console.log(`Setting up link ${index}:`, link.getAttribute('href')); // Debug log

        link.addEventListener('click', function(e) {
            e.preventDefault();
            const href = this.getAttribute('href');

            console.log('Navigation clicked:', href); // Debug log

            if (href === '#home') {
                console.log('Navigating to home');
                showSection('home');
            } else if (href === '#products') {
                console.log('Navigating to products');
                showSection('products');
            } else if (href === '#cart') {
                console.log('Navigating to cart');
                showSection('cart');
            }

            // Update active nav link (except for auth link)
            if (!this.id || this.id !== 'auth-link') {
                navLinks.forEach(l => l.classList.remove('active'));
                this.classList.add('active');
                console.log('Updated active nav link');
            }
        });
    });

    // Set initial active state
    const homeLink = document.querySelector('a[href="#home"]');
    if (homeLink) {
        homeLink.classList.add('active');
        console.log('Set home link as active');
    }

    console.log('Navigation setup complete');
}

function showSection(sectionName) {
    console.log('Showing section:', sectionName); // Debug log

    // Hide all sections first
    const heroSection = document.querySelector('.hero');
    const featuredSections = document.querySelectorAll('.featured-section');
    let productsSection = document.querySelector('.products-section');
    let cartSection = document.querySelector('.cart-section');

    // Hide everything
    if (heroSection) {
        heroSection.style.display = 'none';
        console.log('Hidden hero section');
    }
    featuredSections.forEach(section => {
        section.style.display = 'none';
        console.log('Hidden featured section');
    });
    if (productsSection) {
        productsSection.style.display = 'none';
        console.log('Hidden products section');
    }
    if (cartSection) {
        cartSection.style.display = 'none';
        console.log('Hidden cart section');
    }

    // Show specific section
    if (sectionName === 'home') {
        // Show hero and featured sections
        if (heroSection) {
            heroSection.style.display = 'block';
            console.log('Showing hero section');
        }
        featuredSections.forEach(section => {
            section.style.display = 'block';
            console.log('Showing featured section');
        });
        currentSection = 'home';

    } else if (sectionName === 'products') {
        // Show or create products section
        if (!productsSection) {
            console.log('Creating products section');
            createProductsSection();
        } else {
            productsSection.style.display = 'block';
            console.log('Showing existing products section');
        }
        currentSection = 'products';

    } else if (sectionName === 'cart') {
        // Show or create cart section
        if (!cartSection) {
            console.log('Creating cart section');
            createCartSection();
        } else {
            cartSection.style.display = 'block';
            console.log('Showing existing cart section');
            displayCart(); // Refresh cart content
        }
        currentSection = 'cart';
    }

    console.log('Current section set to:', currentSection);
}

function createProductsSection() {
    const main = document.querySelector('main');

    const productsSection = document.createElement('section');
    productsSection.className = 'products-section';
    productsSection.innerHTML = `
        <div class="container">
            <h2>All Products</h2>
            <div class="filters">
                <input type="text" id="search-input" placeholder="Search products..." onkeyup="filterProducts()">
                <select id="category-filter" onchange="filterProducts()">
                    <option value="all">All Categories</option>
                </select>
                <select id="sort-filter" onchange="filterProducts()">
                    <option value="name">Sort by Name</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="price-high">Price: High to Low</option>
                    <option value="rating">Sort by Rating</option>
                </select>
            </div>
            <div class="products-grid" id="all-products">
                <!-- Products will be loaded here -->
            </div>
        </div>
    `;
    main.appendChild(productsSection);

    // Update category filter and load products
    updateCategoryFilter();
    displayAllProducts();
}

function createCartSection() {
    const main = document.querySelector('main');

    const cartSection = document.createElement('section');
    cartSection.className = 'cart-section';
    cartSection.innerHTML = `
        <div class="container">
            <h2>🛒 Shopping Cart</h2>
            <div id="cart-items">
                <!-- Cart items will be loaded here -->
            </div>
            <div id="cart-summary">
                <!-- Cart summary will be loaded here -->
            </div>
        </div>
    `;
    main.appendChild(cartSection);

    // Load cart content
    displayCart();
}

// Test functions for debugging
window.testNavigation = function() {
    console.log('Testing navigation...');
    console.log('Current section:', currentSection);

    console.log('Testing showSection("products")...');
    showSection('products');

    setTimeout(() => {
        console.log('Testing showSection("home")...');
        showSection('home');
    }, 2000);

    setTimeout(() => {
        console.log('Testing showSection("cart")...');
        showSection('cart');
    }, 4000);
};

window.debugNavigation = function() {
    console.log('=== Navigation Debug Info ===');
    console.log('Current section:', currentSection);
    console.log('Hero section:', document.querySelector('.hero'));
    console.log('Featured sections:', document.querySelectorAll('.featured-section'));
    console.log('Products section:', document.querySelector('.products-section'));
    console.log('Cart section:', document.querySelector('.cart-section'));
    console.log('Nav links:', document.querySelectorAll('.nav-link'));
    console.log('==============================');
};

// Simple navigation function called from HTML onclick
function navigateToSection(sectionName, linkElement) {
    console.log('navigateToSection called:', sectionName);

    // Call the main showSection function
    showSection(sectionName);

    // Update active navigation
    if (linkElement) {
        updateActiveNav(linkElement);
    }

    return false; // Prevent default link behavior
}


function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    console.log(`Getting cookie '${name}':`, cookieValue);
    console.log('All cookies:', document.cookie);
    return cookieValue;
}