from django.urls import path
from .views_simple import (
    user_register, user_login, user_logout,
    user_profile, track_behavior, check_auth, get_csrf_token
)

urlpatterns = [
    # Authentication
    path('register/', user_register, name='user-register'),
    path('login/', user_login, name='user-login'),
    path('logout/', user_logout, name='user-logout'),
    path('check/', check_auth, name='check-auth'),
    path('csrf/', get_csrf_token, name='get-csrf-token'),

    # Profile management
    path('profile/', user_profile, name='user-profile'),

    # User behavior tracking
    path('behavior/', track_behavior, name='user-behavior'),
]
